# -*- coding: utf-8 -*-
"""
Created on Sat Nov  5 11:11:54 2022

@author: tujiu
"""

input = """TcgAward
id	type	cardType	num	reward
int	int	string	int	int
"""


switch = {'int': 'Int',         
          'intarray': 'List<Int>',
          'string': 'String',
          'double': "Double",
          'doublearray': 'List<Double>',
          'stringarray': 'List<String',
          }
switch2 = {'int': '.toInt()',         
          'intarray': '.split(",").map { it.toInt() }',
          'string': '',
          'double': '.toDouble()',
          'doublearray': '.split(",").map { it.toDouble() }',
          'stringarray': '.split(",")'
          }
    
className = input.splitlines()[0]
nameline = input.splitlines()[1]
typeline = input.splitlines()[2]


names = nameline.split('\t')
types = typeline.split('\t')

print("data class " + className + "(")
for name, type in zip(names, types):
    print("    val " + name + ": " + switch.get(type) + ",")
print(")")


print("class " + className + "ConfigParser : ConfigParser<" + className + "> {")
print("    override fun parse(line: String): " + className + " {")
print('        val words = line.split("\\t")')
print("        var i = 0")
for name, type in zip(names, types):
    if name == names[-1]:
        print("        val " + name + " = words[i].trim()" + switch2.get(type))
    else:
        print("        val " + name + " = words[i++].trim()" + switch2.get(type))
    
print("        return " + className + "(")
for name, type in zip(names, types):
    print("            " + name + ",")
print("        )")
print("    }")
print("}")