package com.moyu.core

// taptap
const val tapClientId = "mk6zyyunvza74dr1og"
const val tapClientToken = "PNHV0PNDe15t6wrkprwEIHCK4wgWmW8ZB0NIV4cT"
const val tapServerUrl = "https://mk6zyyun.cloud.tds1.tapapis.cn"


// 好游快爆
const val gameId = "31775"

// bugly 需要更新android manifest
const val buglyId = "4f04fb96bc"


// 存档
const val DS_NAME = "_exceptions"

// 加密密钥
const val INT_ENCRYPT = "kilD?&*%21"

// 正式上线还要注意修改云存档/排行榜的api


// 隐私
const val privacyLink = "https://note.youdao.com/s/JYgPfr8p"

// 许可
const val licenseLink = "https://note.youdao.com/ynoteshare/index.html?id=b1121c2adc048772a46dde7436d3359e&type=note&_time=1661097900185"


const val AD_UNIT_ID_TEST = "ca-app-pub-3940256099942544/**********"
const val AD_UNIT_ID_T1 = "ca-app-pub-5058022002121914/**********"
const val AD_UNIT_ID_T2 = "ca-app-pub-5058022002121914/**********"
const val AD_UNIT_ID_T3 = "ca-app-pub-5058022002121914/**********"


const val appFlyerDevKey = "BerpMdszVXZZsKMmBgkLYm"

const val configPath = "impossible.mp3"

const val merchantId = "**********"
const val APPID = "wx71c62d9f8f031ab7"
const val wcMusic = "battleeffect_16.mp3"