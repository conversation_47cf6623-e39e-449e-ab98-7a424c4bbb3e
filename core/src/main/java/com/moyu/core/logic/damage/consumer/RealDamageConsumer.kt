package com.moyu.core.logic.damage.consumer

import com.moyu.core.logic.battle.BattleField
import com.moyu.core.model.damage.DamageResult
import com.moyu.core.model.damage.DamageType
import com.moyu.core.model.damage.DamageValue
import com.moyu.core.model.role.Role

class RealDamageConsumer(val damageType: DamageType) : DamageConsumer {
    override suspend fun consumeDamage(
        field: BattleField,
        damage: DamageResult,
        doer: Role,
        target: Role
    ): DamageResult {
        val leftDamage = consumeDamageWithShield(
            damage.rawDamage,
            target,
            damageType,
            damage.damageSkill,
            doer
        )
        val finalDamage = consumeDamageWithAllShield(leftDamage, target, damage.damageSkill, doer)
        val damageResult = damage.copy(
            damageValue = DamageValue(
                finalDamage = finalDamage,
                normalShieldBlockedDamage = damage.rawDamage - leftDamage,
                allShieldBlockedDamage = damage.rawDamage - finalDamage,
            )
        )

        saveDamageToRole(field, doer, target, damage, damageResult)
        return damageResult.checkSelfHarm(field, doer, target)
    }
}
