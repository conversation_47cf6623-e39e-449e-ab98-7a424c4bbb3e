package com.moyu.core.model.skill

import com.moyu.core.GameCore


fun Skill.quality(): Int {
    return if (isHeroSkill()) {
        GameCore.instance.getHeroPool().firstOrNull { it.id == id }?.quality ?: 2
    } else if (isBattleSkill()) {
        GameCore.instance.getScrollPool().firstOrNull { it.id == id }?.quality ?: 2
    } else {
        if (elementType == 0) {
            2
        } else {
            GameCore.instance.getRacePool().firstOrNull { it.skillId.contains(id) }?.quality ?: 2
        }
    }
}