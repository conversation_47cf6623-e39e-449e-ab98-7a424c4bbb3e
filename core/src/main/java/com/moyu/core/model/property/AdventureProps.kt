package com.moyu.core.model.property

import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.R
import com.moyu.core.model.level.ReputationLevel
import kotlinx.serialization.Serializable

val EMPTY_ADV_PROPS = AdventureProps()

@Serializable
data class AdventureProps(
    val age: Int = 0,
    // 冒险属性
    val science: Int = 0,
    val politics: Int = 0,
    val military: Int = 0,
    val religion: Int = 0,
    val commerce: Int = 0,
    val art: Int = 0,
    val population: Int = 0,

    val gainExp: Int = 0,
    val gainMoney: Int = 0,
    val gainReputation: Int = 0,
    val shopPriceMercenary: Int = 0,
    val winEventPlayIds: List<Int> = emptyList(),
    val failEventPlayIds: List<Int> = emptyList()
) {

    companion object {

        fun getRoleProperty(type: Int, diff: Int): AdventureProps {
            return when (type) {
                1 -> AdventureProps(science = diff)
                2 -> AdventureProps(politics = diff)
                3 -> AdventureProps(military = diff)
                4 -> AdventureProps(religion = diff)
                5 -> AdventureProps(commerce = diff)
                6 -> AdventureProps(art = diff)
                7 -> AdventureProps(population = diff)
                else -> AdventureProps()
            }
        }

        fun rolePropertyName(i: Int): String {
            return when (i) {
                1 -> AppWrapper.getString(R.string.prop1)
                2 -> AppWrapper.getString(R.string.prop2)
                3 -> AppWrapper.getString(R.string.prop3)
                4 -> AppWrapper.getString(R.string.prop4)
                5 -> AppWrapper.getString(R.string.prop5)
                6 -> AppWrapper.getString(R.string.prop6)
                7 -> AppWrapper.getString(R.string.populations)
                else -> error("无效属性")
            }
        }

        fun getReputationLevel(targetReputation: Int): Int {
            return GameCore.instance.getReputationLevelPool().reversed()
                .firstOrNull { it.expTotal <= targetReputation }?.level
                ?: GameCore.instance.getReputationLevelPool().last().level
        }

        fun getReputationLevelData(targetReputation: Int): ReputationLevel {
            return GameCore.instance.getReputationLevelPool().reversed()
                .firstOrNull { it.expTotal <= targetReputation }
                ?: GameCore.instance.getReputationLevelPool().last()
        }

        fun getPopulationLevelData(population: Int): ReputationLevel {
            return GameCore.instance.getPopulationLevelPool().reversed()
                .firstOrNull { it.expTotal <= population }
                ?: if (population < 0) GameCore.instance.getPopulationLevelPool()
                    .first() else GameCore.instance.getPopulationLevelPool().last()
        }

        fun createNew(): AdventureProps {
            val initProps = GameCore.instance.getInitAdvProps()
            return AdventureProps(
                age = 0,
                science = initProps[0],
                politics = initProps[1],
                military = initProps[2],
                religion = initProps[3],
                commerce = initProps[4],
                art = initProps[5],
                population = initProps[6],
            )
        }
    }

    operator fun plus(diffProperty: AdventureProps): AdventureProps {
        return copy(
            science = science + diffProperty.science,
            politics = politics + diffProperty.politics,
            military = military + diffProperty.military,
            religion = religion + diffProperty.religion,
            commerce = commerce + diffProperty.commerce,
            age = age + diffProperty.age,
            art = art + diffProperty.art,
            population = population + diffProperty.population,


            gainExp = gainExp + diffProperty.gainExp,
            gainMoney = gainMoney + diffProperty.gainMoney,
            gainReputation = gainReputation + diffProperty.gainReputation,
            shopPriceMercenary = shopPriceMercenary + diffProperty.shopPriceMercenary,
            winEventPlayIds = winEventPlayIds + diffProperty.winEventPlayIds,
            failEventPlayIds = failEventPlayIds + diffProperty.failEventPlayIds,
        )
    }

    operator fun minus(diffProperty: AdventureProps): AdventureProps {
        return copy(
            science = science - diffProperty.science,
            politics = politics - diffProperty.politics,
            military = military - diffProperty.military,
            religion = religion - diffProperty.religion,
            commerce = commerce - diffProperty.commerce,
            age = age - diffProperty.age,
            art = art - diffProperty.art,
            population = population - diffProperty.population,

            gainExp = gainExp - diffProperty.gainExp,
            gainMoney = gainMoney - diffProperty.gainMoney,
            gainReputation = gainReputation - diffProperty.gainReputation,
            shopPriceMercenary = shopPriceMercenary - diffProperty.shopPriceMercenary,
            winEventPlayIds = winEventPlayIds - diffProperty.winEventPlayIds.toSet(),
            failEventPlayIds = failEventPlayIds - diffProperty.failEventPlayIds.toSet(),
        )
    }


    operator fun times(times: Int): AdventureProps {
        return this.copy(
            science = science * times,
            politics = politics * times,
            military = military * times,
            commerce = commerce * times,
            religion = religion * times,
            art = art * times,
            population = population * times,
        )
    }

    fun isPunish(): Boolean {
        return science < 0 || politics < 0 || military < 0 || religion < 0 || commerce < 0 || age < 0 || art < 0 || population < 0
    }

    fun isNotEmpty(): Boolean {
        return this != EMPTY_ADV_PROPS
    }

    fun getPropertyByTarget(propertyEnum: Int): Int {
        return when (propertyEnum) {
            1 -> {
                science
            }

            2 -> {
                politics
            }

            3 -> {
                military
            }

            4 -> {
                religion
            }

            5 -> {
                commerce
            }

            6 -> {
                art
            }

            7 -> {
                population
            }

            else -> {
                error("无效effectReference")
            }
        }
    }

    fun getPopulationLevel(): ReputationLevel {
        return GameCore.instance.getPopulationLevelPool().reversed()
            .firstOrNull { it.expTotal <= this.population }
            ?: GameCore.instance.getPopulationLevelPool().last()
    }

    fun perBiggerI(property: AdventureProps): Boolean {
        return this.science >= property.science &&
                this.politics >= property.politics &&
                this.military >= property.military &&
                this.religion >= property.religion &&
                this.commerce >= property.commerce &&
                this.art >= property.art &&
                this.population >= property.population
    }

    fun ensureNonNegative(): AdventureProps {
        return copy(
            science = science.coerceAtLeast(0),
            politics = politics.coerceAtLeast(0),
            military = military.coerceAtLeast(0),
            religion = religion.coerceAtLeast(0),
            commerce = commerce.coerceAtLeast(0),
            art = art.coerceAtLeast(0),
            population = population,
        )
    }

    fun ensureStartPopulation(): AdventureProps {
        return copy(
            population = population.coerceAtLeast(1),
        )
    }
}
