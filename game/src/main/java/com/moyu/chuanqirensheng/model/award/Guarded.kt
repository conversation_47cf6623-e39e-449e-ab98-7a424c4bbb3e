package com.moyu.chuanqirensheng.model.award

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.snapshots.SnapshotStateList
import androidx.compose.runtime.snapshots.StateObject
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_CHEATING
import com.moyu.chuanqirensheng.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.datastore.setLongValueByKey
import com.moyu.core.util.RANDOM
import com.moyu.core.util.nextIntClosure
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

fun cheating() {
    GameApp.globalScope.launch {
        setBooleanValueByKey(KEY_CHEATING, true)
        delay(200)
        error("作弊:${GameApp.instance.getObjectId()}")
    }
}

class Guarded(val key: String, private val mutableState: MutableState<Int> = mutableStateOf(0)) :
    MutableState<Int> by mutableState {
    private val randomGuard = RANDOM.nextIntClosure(2653, 88635)
    private var guardValue: Int = 0

    init {
        mutableState.value = getIntFlowByKey(key, mutableState.value)
        guardValue = mutableState.value - randomGuard
    }

    fun save() {
        setIntValueByKey(key, mutableState.value)
    }

    override var value: Int
        get() = mutableState.value
        set(value) {
            if (mutableState.value != guardValue + randomGuard) {
                cheating()
            } else {
                mutableState.value = value
                guardValue = value - randomGuard
                save()
            }
        }
}


class GuardedL(val key: String, private val mutableState: MutableState<Long> = mutableStateOf(0L)) :
    MutableState<Long> by mutableState {
    private val randomGuard = RANDOM.nextIntClosure(2653, 88635).toLong()
    private var guardValue: Long = 0

    init {
        mutableState.value = getLongFlowByKey(key, mutableState.value)
        guardValue = mutableState.value - randomGuard
    }

    fun save() {
        setLongValueByKey(key, mutableState.value)
    }

    override var value: Long
        get() = mutableState.value
        set(value) {
            if (mutableState.value != guardValue + randomGuard) {
                cheating()
            } else {
                mutableState.value = value
                guardValue = value - randomGuard
                save()
            }
        }
}

class GuardedB(
    val key: String,
    private val mutableState: MutableState<Boolean> = mutableStateOf(false),
) : MutableState<Boolean> by mutableState {
    private val randomGuard = RANDOM.nextIntClosure(2653, 88635)
    private var guardValue: Int = 0

    init {
        mutableState.value = getBooleanFlowByKey(key)
        guardValue = (if (mutableState.value) 1 else 0) - randomGuard
    }

    fun save() {
        setBooleanValueByKey(key, mutableState.value)
    }

    override var value: Boolean
        get() = mutableState.value
        set(value) {
            val intValue = if (mutableState.value) 1 else 0
            if (intValue != guardValue + randomGuard) {
                cheating()
            } else {
                mutableState.value = value
                guardValue = (if (mutableState.value) 1 else 0) - randomGuard
                save()
            }
        }
}


class GuardedMemoryList(val mutableList: SnapshotStateList<Int> = mutableStateListOf()) :
    MutableList<Int> by mutableList, StateObject by mutableList {
    private val randomGuard = RANDOM.nextIntClosure(2653, 88635)
    private var guardValues: MutableList<Int> =
        mutableList.toList().map { it - randomGuard }.toMutableList()

    override fun get(index: Int): Int {
        if (guardValues[index] + randomGuard != mutableList[index]) {
            cheating()
        }
        return mutableList[index]
    }

    override fun clear() {
        mutableList.clear()
        guardValues.clear()
    }

    override fun set(index: Int, element: Int): Int {
        guardValues[index] = element - randomGuard
        return mutableList.set(index, element)
    }

    override fun addAll(elements: Collection<Int>): Boolean {
        guardValues.addAll(elements.map { it - randomGuard })
        return mutableList.addAll(elements)
    }
}


class GuardedMemoryF(private val mutableState: MutableState<Float> = mutableStateOf(0f)) :
    MutableState<Float> by mutableState {
    private val randomGuard = RANDOM.nextIntClosure(2653, 88635).toFloat()
    private var guardValue: Float = 0f

    init {
        guardValue = mutableState.value - randomGuard
    }

    override var value: Float
        get() = mutableState.value
        set(value) {
            if (mutableState.value != guardValue + randomGuard) {
                cheating()
            } else {
                mutableState.value = value
                guardValue = value - randomGuard
            }
        }
}

class GuardedMemory(private val mutableState: MutableState<Int> = mutableIntStateOf(0)) :
    MutableState<Int> by mutableState {
    private val randomGuard = RANDOM.nextIntClosure(2653, 88635)
    private var guardValue: Int = 0

    init {
        guardValue = mutableState.value - randomGuard
    }

    override var value: Int
        get() = mutableState.value
        set(value) {
            if (mutableState.value != guardValue + randomGuard) {
                cheating()
            } else {
                mutableState.value = value
                guardValue = value - randomGuard
            }
        }
}