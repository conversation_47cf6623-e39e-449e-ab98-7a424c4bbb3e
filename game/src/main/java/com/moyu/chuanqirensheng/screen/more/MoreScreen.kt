@file:OptIn(ExperimentalLayoutApi::class, ExperimentalLayoutApi::class)

package com.moyu.chuanqirensheng.screen.more

import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.core.content.ContextCompat
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.DecorateTextField
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.getTextStyle
import com.moyu.chuanqirensheng.screen.skill.IconView
import com.moyu.chuanqirensheng.ui.theme.RaceNameColor
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.paddingHuge
import com.moyu.chuanqirensheng.ui.theme.paddingHugePlus
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.util.ABOUT_SCREEN
import com.moyu.chuanqirensheng.util.FAMOUS_SCREEN
import com.moyu.chuanqirensheng.util.RANK_SCREEN
import com.moyu.chuanqirensheng.util.SETTING_SCREEN
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.chuanqirensheng.util.goto

data class MoreItem(
    val name: String,
    val route: () -> Unit,
    val icon: String,
    val blockOnGooglePlay: Boolean = false
)

val moreItems = listOf(
    MoreItem(
        name = GameApp.instance.getWrapString(R.string.tutor),
        route = { Dialogs.tutorDialog.value = true },
        icon = "more_icon_teach"
    ),
    MoreItem(
        name = GameApp.instance.getWrapString(R.string.setting),
        route = { goto(SETTING_SCREEN) },
        icon = "more_icon_setup"
    ),
    MoreItem(
        name = GameApp.instance.getWrapString(R.string.rank_title),
        route = { goto(RANK_SCREEN) },
        icon = "more_icon_rank"
    ),
    MoreItem(
        name = GameApp.instance.getWrapString(R.string.famous),
        route = { goto(FAMOUS_SCREEN) },
        icon = "menu_famous"
    ),
    MoreItem(
        name = GameApp.instance.getWrapString(R.string.about),
        route = { goto(ABOUT_SCREEN) },
        icon = "new_tutor_icon"
    ),
    MoreItem(
        name = GameApp.instance.getWrapString(R.string.new_tutor),
        route = {
            val link = GameApp.instance.resources.getString(R.string.luntan_link)
            val uri: Uri = Uri.parse(link)
            val intent = Intent(Intent.ACTION_VIEW, uri)
            ContextCompat.startActivity(GameApp.instance.activity, intent, Bundle())
        },
        icon = "more_icon_about",
        blockOnGooglePlay = true
    ),
).filterNot { GameApp.instance.resources.getBoolean(R.bool.has_google_service) && it.blockOnGooglePlay }
    .filter {
    if (GameApp.instance.canDoNetwork()) true else it.name !in listOf(
        GameApp.instance.getWrapString(R.string.rank_title),
        GameApp.instance.getWrapString(R.string.famous),
    )
}


@Composable
fun MoreScreen() {
    GameBackground(title = stringResource(R.string.more)) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            modifier = Modifier
                .fillMaxSize()
                .paint(
                    painterResource(id = R.drawable.common_page_frame),
                    contentScale = ContentScale.FillBounds
                )
        ) {
            Spacer(modifier = Modifier.size(paddingMedium))
            LazyVerticalGrid(
                modifier = Modifier.fillMaxWidth(),
                columns = GridCells.Fixed(3),
                content = {
                    items(moreItems.size) { index ->
                        OneItem(moreItem = moreItems[index], size = ItemSize.HugeTalent)
                    }
                }
            )
            val text = remember {
                mutableStateOf("")
            }

            Spacer(modifier = Modifier.size(paddingHugePlus))
            Column(
                modifier = Modifier
                    .align(Alignment.Start)
                    .padding(horizontal = paddingHuge)
            ) {

                DecorateTextField(text.value) {
                    text.value = it
                }
                Spacer(modifier = Modifier.size(paddingMedium))
                GameButton(
                    modifier = Modifier.align(Alignment.CenterHorizontally),
                    enabled = text.value.isNotEmpty(),
                    onClick = {
                        AwardManager.doNetAward(text.value)
                    },
                    text = stringResource(R.string.exchange),
                    textColor = Color.White,
                    buttonStyle = ButtonStyle.Orange,
                    buttonSize = ButtonSize.Big
                )
                Spacer(modifier = Modifier.size(paddingMedium))
                Text(
                    text = stringResource(R.string.common_codes),
                    style = MaterialTheme.typography.h3
                )
                Spacer(modifier = Modifier.size(paddingSmallPlus))
            }
            Spacer(modifier = Modifier.size(paddingMedium))
            if (GameApp.instance.resources.getBoolean(R.bool.has_google_service)) {
                Row(modifier = Modifier.fillMaxWidth().padding(horizontal = padding12).clickable {
                    val myClipboard =
                        GameApp.instance.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                    val myClip = ClipData.newPlainText("text", "<EMAIL>")
                    myClipboard.setPrimaryClip(myClip)
                    ("gmail：<EMAIL> " + GameApp.instance.getWrapString(R.string.copied)).toast()
                }) {
                    Text(
                        text = stringResource(R.string.contact1) + "(email)：",
                        style = MaterialTheme.typography.h3
                    )
                    Text(
                        text = "<EMAIL>",
                        style = MaterialTheme.typography.h3,
                        textDecoration = TextDecoration.Underline,
                        color = RaceNameColor
                    )
                }
                Row(modifier = Modifier.fillMaxWidth().padding(horizontal = padding12).clickable {
                    val myClipboard =
                        GameApp.instance.getSystemService(Context.CLIPBOARD_SERVICE) as ClipboardManager
                    val myClip = ClipData.newPlainText("text", "<EMAIL>")
                    myClipboard.setPrimaryClip(myClip)
                    ("gmail：<EMAIL> " + GameApp.instance.getWrapString(R.string.copied)).toast()
                }) {
                    Text(
                        text = stringResource(R.string.contact1) + "(email)：",
                        style = MaterialTheme.typography.h3
                    )
                    Text(
                        text = "<EMAIL>",
                        style = MaterialTheme.typography.h3,
                        textDecoration = TextDecoration.Underline,
                        color = RaceNameColor
                    )
                }
            }
            Spacer(modifier = Modifier.size(paddingMedium))
        }
    }
}

@Composable
fun OneItem(moreItem: MoreItem, size: ItemSize = ItemSize.LargePlus, callback: () -> Unit = {}) {
    Column(horizontalAlignment = Alignment.CenterHorizontally, modifier = Modifier.fillMaxWidth()) {
        IconView(
            itemSize = ItemSize.Huge,
            res = getImageResourceDrawable(moreItem.icon),
            frame = R.drawable.item_quality_3,
            name = moreItem.name
        ) {
            callback()
            moreItem.route()
        }
        Spacer(modifier = Modifier.size(paddingSmall))
        Text(text = moreItem.name, style = size.getTextStyle(), textAlign = TextAlign.Center)
        Spacer(modifier = Modifier.size(paddingMediumPlus))
    }
}
