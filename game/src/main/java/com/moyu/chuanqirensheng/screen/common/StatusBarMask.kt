package com.moyu.chuanqirensheng.screen.common

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.screen.movie.VideoPlayer2
import com.moyu.chuanqirensheng.ui.theme.B04
import com.moyu.chuanqirensheng.ui.theme.B35
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.chuanqirensheng.util.immersionBarHeightInDp
import kotlinx.coroutines.delay

const val GAME_BG_PIC = "game_bg"


@Composable
fun StatusBarMask() {
    Box(
        modifier = Modifier
            .height(immersionBarHeightInDp)
            .fillMaxWidth()
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(B50, B04)
                )
            )
    )
}

@Composable
fun GameBackColor() {
    val showVideo = remember {
        mutableStateOf(false)
    }
    LaunchedEffect(Unit) {
        delay(1200)
        showVideo.value = true
    }
    if (showVideo.value) {
        VideoPlayer2(modifier = Modifier.fillMaxSize())
    }
    AnimatedVisibility(
        visible = !showVideo.value,
        enter = fadeIn(),
        exit = fadeOut(),
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.Crop,
            painter = painterResource(id = getImageResourceDrawable(GAME_BG_PIC)),
            contentDescription = null
        )
    }
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(B35)
    )
}