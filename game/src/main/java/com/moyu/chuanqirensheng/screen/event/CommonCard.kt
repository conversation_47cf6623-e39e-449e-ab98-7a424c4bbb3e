package com.moyu.chuanqirensheng.screen.event

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.common.CardSize
import com.moyu.chuanqirensheng.screen.common.getTextStyle
import com.moyu.chuanqirensheng.screen.sensor.ShadowedView
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.util.getImageResourceDrawable


@Composable
fun CommonCard2(
    modifier: Modifier = Modifier,
    cardSize: CardSize,
    title: String,
    icon: String,
    titleColor: Color = Color.White,
    frame: Int = R.drawable.main_frame_1,
) {
    ShadowedView(cardSize = cardSize) {
        Box(modifier.size(cardSize.width, cardSize.height)) {
            Image(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding1)
                    .clip(RoundedCornerShape(cardSize.getRadius())),
                contentScale = ContentScale.Crop,
                painter = painterResource(id = getImageResourceDrawable(icon)),
                contentDescription = title
            )
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = frame),
                contentDescription = null
            )
            CardTitleView2(
                Modifier
                    .align(Alignment.BottomCenter), title, cardSize, titleColor
            )
        }
    }
}

@Composable
fun CommonCard(
    modifier: Modifier = Modifier,
    cardSize: CardSize,
    title: String,
    icon: String,
    titleColor: Color = Color.White,
    frame: Int = R.drawable.main_frame_1,
) {
    ShadowedView(cardSize = cardSize) {
        Box(modifier.size(cardSize.width, cardSize.height)) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = frame),
                contentDescription = null
            )
            Image(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding1)
                    .clip(RoundedCornerShape(cardSize.getRadius())),
                contentScale = ContentScale.Crop,
                painter = painterResource(id = getImageResourceDrawable(icon)),
                contentDescription = null
            )
            CardTitleView(
                Modifier
                    .align(Alignment.TopCenter), title, cardSize, titleColor
            )
        }
    }
}

@Composable
fun CardTitleView(modifier: Modifier, title: String, cardSize: CardSize, titleColor: Color) {
    Box(
        modifier = modifier.fillMaxWidth(),
        contentAlignment = Alignment.TopCenter
    ) {
        Image(
            modifier = Modifier.fillMaxWidth(),
            contentScale = ContentScale.FillWidth,
            painter = painterResource(id = R.drawable.card_label),
            contentDescription = null
        )
        Box(modifier = Modifier.height(cardSize.height / 6f), contentAlignment = Alignment.Center) {
            Text(
                text = title,
                style = cardSize.getTextStyle(),
                textAlign = TextAlign.Center,
                color = titleColor
            )
        }
    }
}


@Composable
fun CardTitleView2(modifier: Modifier, title: String, cardSize: CardSize, titleColor: Color) {
    Box(
        modifier = modifier.fillMaxWidth(),
        contentAlignment = Alignment.TopCenter
    ) {
        Text(
            modifier = Modifier
                .padding(bottom = cardSize.height / 16f),
            text = title,
            style = cardSize.getTextStyle(),
            textAlign = TextAlign.Center,
            color = titleColor
        )
    }
}