package com.moyu.chuanqirensheng.screen.equip

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.skill.getFrameDrawable
import com.moyu.chuanqirensheng.logic.skill.getHeroTouchInfo
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.getTextStyle
import com.moyu.chuanqirensheng.screen.more.Stars
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingTiny
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isAdventure
import com.moyu.core.model.skill.quality
import kotlin.math.roundToInt


@Composable
fun SingleHeroView(
    hero: Skill,
    itemSize: ItemSize = ItemSize.Large,
    showName: Boolean = true,
    showNum: Boolean = false,
    locked: Boolean = false,
    textColor: Color = Color.Black,
    showStars: Boolean = true,
    showRed: Boolean = false,
    showEffect: Boolean = false,
    callback: (Skill) -> Unit = { Dialogs.heroDetailDialog.value = hero }
) {
    Column(modifier = Modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        EffectButton(onClick = {
            callback(hero)
        }) {
            Image(
                modifier = Modifier.size(itemSize.frameSize),
                painter = painterResource(hero.getFrameDrawable()),
                contentDescription = null,
            )
            Image(
                modifier = Modifier
                    .size(itemSize.itemSize)
                    .clip(RoundedCornerShape(itemSize.itemSize / 12)),
                contentScale = ContentScale.Crop,
                alignment = Alignment.TopCenter,
                painter = painterResource(getImageResourceDrawable(hero.icon)),
                contentDescription = hero.getHeroTouchInfo(),
            )
            if (hero.extraInfo.isNotEmpty()) {
                Box(
                    modifier = Modifier.background(B50).padding(paddingSmall)) {
                    Text(text = hero.extraInfo, style = MaterialTheme.typography.body1)
                }
            }
            if (!hero.isAdventure() && showStars) {
                Stars(
                    modifier = Modifier
                        .align(Alignment.BottomCenter)
                        .padding(bottom = itemSize.itemSize / 12),
                    hero.level,
                    starHeight = itemSize.itemSize / 5
                )
            }
            if (locked) {
                Image(
                    modifier = Modifier.size(itemSize.itemSize),
                    painter = painterResource(R.drawable.common_lock),
                    contentDescription = null,
                )
            }
            if (showRed && hero.new) {
                Image(
                    modifier = Modifier
                        .align(Alignment.TopEnd)
                        .size(imageSmall),
                    painter = painterResource(R.drawable.red_icon),
                    contentDescription = null
                )
            }
            if (showEffect && hero.quality() >= 3) {
                val infiniteTransition = rememberInfiniteTransition(label = "")
                val index = infiniteTransition.animateFloat(
                    initialValue = 1f, targetValue = 50f, animationSpec = infiniteRepeatable(
                        animation = tween(1600, easing = LinearEasing),
                        repeatMode = RepeatMode.Restart,
                    ), label = ""
                )
                if (index.value.roundToInt() <= 15) { // 做一个间歇的效果
                    Image(
                        modifier = Modifier
                            .size(itemSize.frameSize)
                            .scale(1.3f),
                        painter = painterResource(
                            getImageResourceDrawable(
                                "rewarditem_${index.value.roundToInt()}"
                            )
                        ),
                        contentDescription = null
                    )
                }
            }
        }
        if (showName) {
            Spacer(modifier = Modifier.size(paddingTiny))
            val text = if (showNum) hero.name + "x${hero.num}" else hero.name
            Text(
                text = text,
                style = itemSize.getTextStyle(),
                maxLines = 2,
                minLines = 2,
                textAlign = TextAlign.Center,
                color = textColor
            )
        }
    }
}
