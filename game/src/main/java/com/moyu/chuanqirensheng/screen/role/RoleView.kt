@file:OptIn(ExperimentalLayoutApi::class)

package com.moyu.chuanqirensheng.screen.role

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.style.TextAlign
import coil.compose.rememberAsyncImagePainter
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.award.toElementIcon
import com.moyu.chuanqirensheng.logic.award.toElementToast
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.screen.common.CommonElementPoint
import com.moyu.chuanqirensheng.screen.common.TitleCloseButton
import com.moyu.chuanqirensheng.screen.property.RolePropertyViewInGame
import com.moyu.chuanqirensheng.ui.theme.countryLevelHeight
import com.moyu.chuanqirensheng.ui.theme.countryLevelWidth
import com.moyu.chuanqirensheng.ui.theme.elementHeight
import com.moyu.chuanqirensheng.ui.theme.elementWidth
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.padding7
import com.moyu.chuanqirensheng.ui.theme.paddingLargePlus
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.ui.theme.paddingTiny
import com.moyu.chuanqirensheng.ui.theme.userHeadHeight
import com.moyu.chuanqirensheng.ui.theme.userHeadImageSize
import com.moyu.chuanqirensheng.ui.theme.userHeadWidth
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.chuanqirensheng.util.immersionBarHeightInDp
import com.moyu.core.model.level.ReputationLevel

/**
 * 要处理状态栏
 */
@Composable
fun CountryInfoView(modifier: Modifier = Modifier) {
    Column(
        modifier = modifier
            .paint(
                painterResource(id = R.drawable.event_big_top2),
                contentScale = ContentScale.FillBounds
            )
            .padding(top = immersionBarHeightInDp)
            .padding(horizontal = paddingTiny)
    ) {
        Box {
            Row(modifier = Modifier.fillMaxWidth()) {
                CountryLevelView(BattleManager.adventureProps.value.getPopulationLevel())
                RolePropertyViewInGame(
                    modifier = Modifier
                        .fillMaxWidth()
                        .graphicsLayer {
                            translationY = paddingSmall.toPx()
                        },
                    property = BattleManager.adventureProps.value
                )
            }
            TitleCloseButton(modifier = Modifier.align(Alignment.TopEnd)) {
                GameApp.instance.activity.onBackPressedDispatcher.onBackPressed()
            }
        }
        CurrentElements(
            Modifier
                .fillMaxWidth()
                .padding(paddingMedium)
        )
    }
}

@Composable
fun CountryLevelView(populationLevel: ReputationLevel) {
    Box(
        modifier = Modifier.size(countryLevelWidth, countryLevelHeight),
        contentAlignment = Alignment.Center
    ) {
        Image(
            painter = painterResource(id = getImageResourceDrawable("event_empire${populationLevel.level}")),
            contentScale = ContentScale.FillHeight,
            contentDescription = null
        )
        Text(
            modifier = Modifier.graphicsLayer {
                translationY = -paddingSmall.toPx()
            },
            text = populationLevel.name,
            style = MaterialTheme.typography.h4
        )
        TextLabel(
            Modifier
                .align(Alignment.BottomCenter)
                .padding(horizontal = paddingMedium),
            BattleManager.countryName.value,
        )
    }
}

@Composable
fun TextLabel(modifier: Modifier, text: String, style: TextStyle = MaterialTheme.typography.h6) {
    Box(modifier = modifier.graphicsLayer {
        translationY = -paddingMedium.toPx()
    }, contentAlignment = Alignment.Center) {
        Image(
            painter = painterResource(id = R.drawable.event_condition_label),
            contentScale = ContentScale.FillWidth,
            contentDescription = null
        )
        Text(text = text, style = style, maxLines = 1, textAlign = TextAlign.Center)
    }
}

@Composable
fun CurrentElements(modifier: Modifier) {
    FlowRow(modifier = modifier, maxItemsInEachRow = 3, overflow = FlowRowOverflow.Visible) {
        BattleManager.elements.forEachIndexed { index, i ->
            Box(modifier = Modifier.padding(paddingTiny)) {
                val mark = if (BattleManager.extraElements[index] > 0) "+" else ""
                CommonElementPoint(
                    money = "${i}(${mark}${BattleManager.extraElements[index]}/${stringResource(id = com.moyu.core.R.string.year)})",
                    toast = index.toElementToast(),
                    drawable = index.toElementIcon()
                )
            }
            if (index == 2) {
                // todo 要求对齐方式比较特殊，这里空一个
                Spacer(
                    modifier = Modifier
                        .padding(paddingTiny)
                        .size(elementWidth, elementHeight)
                )
            }
        }
    }
}

@Composable
fun UserImageView(
    modifier: Modifier = Modifier,
    headRes: String? = null,
    name: String? = null,
) {
    Box(modifier = modifier) {
        Box(Modifier.align(Alignment.CenterStart)) {
            name?.let {
                Image(
                    modifier = Modifier
                        .align(Alignment.CenterEnd)
                        .height(userHeadHeight * 2 / 3)
                        .width(userHeadWidth)
                        .graphicsLayer {
                            translationX = paddingLargePlus.toPx()
                        },
                    painter = painterResource(id = R.drawable.main_name_frame),
                    contentScale = ContentScale.FillBounds,
                    contentDescription = null
                )
            }
            Image(
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .size(userHeadHeight)
                    .graphicsLayer {
                        translationY = padding1.toPx()
                        translationX = -padding1.toPx()
                    },
                painter = painterResource(id = R.drawable.talent_learned_new),
                contentScale = ContentScale.FillBounds,
                contentDescription = null
            )
        }
        name?.let {
            Text(
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .padding(start = userHeadHeight + paddingSmallPlus),
                text = name,
                style = MaterialTheme.typography.h3,
            )
        }
        headRes?.let {
            Image(
                // todo
                painter = if (it.startsWith("http")) rememberAsyncImagePainter(it) else painterResource(
                    id = getImageResourceDrawable(it)
                ),
                contentScale = ContentScale.Crop,
                alignment = Alignment.TopCenter,
                modifier = Modifier
                    .align(Alignment.CenterStart)
                    .padding(start = padding7)
                    .size(userHeadImageSize)
                    .clip(RoundedCornerShape(userHeadImageSize / 2)),
                contentDescription = null
            )
        }
    }
}

@Composable
fun EventAgeView(modifier: Modifier = Modifier) {
    Text(
        modifier = modifier,
        text = "${BattleManager.adventureProps.value.age}" + GameApp.instance.getWrapString(R.string.age),
        style = MaterialTheme.typography.h4
    )
}