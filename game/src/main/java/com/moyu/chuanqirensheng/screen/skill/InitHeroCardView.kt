@file:OptIn(ExperimentalLayoutApi::class)

package com.moyu.chuanqirensheng.screen.skill

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.core.animateIntAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.ally.MAX_HERO_SIZE
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.equip.SingleHeroView
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.paddingTiny
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.util.RANDOM
import com.moyu.core.util.nextIntClosure
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


@Composable
fun InitHeroCardView(modifier: Modifier) {
    FlowRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceEvenly,
        overflow = FlowRowOverflow.Visible,
        maxItemsInEachRow = MAX_HERO_SIZE / 2
    ) {
        val skills = BattleManager.getGameHeroes()
        var skillCount = 0
        repeat(MAX_HERO_SIZE) { index ->
            val unlock = UnlockManager.getInitHeroUnlockByIndex(index)
            val unlocked = UnlockManager.getUnlockedFlow(unlock)
            Box(modifier = Modifier.padding(vertical = paddingTiny)) {
                val gif = remember {
                    mutableStateOf(false)
                }
                skills.getOrNull(skillCount)?.takeIf { unlocked }?.let { skill ->
                    skillCount += 1
                    SingleHeroView(
                        hero = if (BattleManager.heroManualEffect.value) skill.copy(extraInfo = stringResource(
                            R.string.not_effected
                        )
                        ) else skill,
                        showName = false,
                        itemSize = ItemSize.LargePlus
                    ) {
                        GameApp.globalScope.launch(Dispatchers.Main) {
                            BattleManager.selectHeroToGame(it)
                        }
                    }
                    GifView(modifier = Modifier.size(ItemSize.LargePlus.frameSize), gif.value, 8, "equipon_")
                } ?: run {
                    gif.value = true
                    Box(contentAlignment = Alignment.Center) {
                        EmptyIconView(itemSize = ItemSize.LargePlus) {
                            if (unlocked) {
                                Dialogs.selectHeroToGameDialog.value = true
                            } else {
                                unlock.desc.toast()
                            }
                        }
                        if (!unlocked) {
                            Image(
                                modifier = Modifier.size(imageLarge),
                                painter = painterResource(R.drawable.common_lock),
                                contentDescription = stringResource(id = R.string.locked),
                            )
                        }
                    }
                }
            }
        }
    }
}


@Composable
fun GifView(modifier: Modifier, enabled: Boolean, gifCount: Int, gifDrawable: String, pace: Int = 1, end:()->Unit = {}) {
    val switch = remember { mutableStateOf(false) }
    LaunchedEffect(enabled) {
        delay(RANDOM.nextIntClosure(10, 80).toLong())
        switch.value = enabled
    }
    val effectIndex by animateIntAsState(
        targetValue = if (switch.value) gifCount else 1,
        animationSpec = TweenSpec(
            durationMillis = gifCount * pace * 33,
            easing = LinearEasing
        ),
        finishedListener = {
            switch.value = false
            end()
        }, label = ""
    )
    if (switch.value) {
        Image(
            painter = painterResource(
                id = getImageResourceDrawable(
                    "${gifDrawable}${effectIndex}"
                )
            ),
            contentDescription = null,
            modifier = modifier.scale(2f),
            contentScale = ContentScale.FillHeight
        )
    }
}
