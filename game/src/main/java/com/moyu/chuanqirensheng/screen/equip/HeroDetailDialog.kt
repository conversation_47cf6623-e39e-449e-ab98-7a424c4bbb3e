package com.moyu.chuanqirensheng.screen.equip

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontFamily
import androidx.compose.ui.text.font.FontStyle
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.skill.canStarUp
import com.moyu.chuanqirensheng.logic.skill.empire
import com.moyu.chuanqirensheng.logic.skill.getCardFrameDrawable
import com.moyu.chuanqirensheng.logic.skill.getNextLevelSkill
import com.moyu.chuanqirensheng.logic.skill.getRealDescColorful
import com.moyu.chuanqirensheng.logic.skill.story
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.CardSize
import com.moyu.chuanqirensheng.screen.common.CurrentDiamondPoint
import com.moyu.chuanqirensheng.screen.common.DiamondPoint
import com.moyu.chuanqirensheng.screen.dialog.CommonDialog
import com.moyu.chuanqirensheng.screen.effect.newTurnEffectState
import com.moyu.chuanqirensheng.screen.effect.restartEffect
import com.moyu.chuanqirensheng.screen.effect.starUpEffect
import com.moyu.chuanqirensheng.screen.more.Stars
import com.moyu.chuanqirensheng.screen.sensor.ShadowedView
import com.moyu.chuanqirensheng.screen.skill.getElementTypeRes
import com.moyu.chuanqirensheng.screen.skill.getSkillDescColor
import com.moyu.chuanqirensheng.text.getHeroElementName
import com.moyu.chuanqirensheng.text.getTypeName
import com.moyu.chuanqirensheng.thread.gameDispatcher
import com.moyu.chuanqirensheng.ui.theme.W50
import com.moyu.chuanqirensheng.ui.theme.cardStarBigSize
import com.moyu.chuanqirensheng.ui.theme.eventCardBigHeight
import com.moyu.chuanqirensheng.ui.theme.eventCardBigWidth
import com.moyu.chuanqirensheng.ui.theme.imageLargePlus
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.moneyHeight
import com.moyu.chuanqirensheng.ui.theme.moneyWidth
import com.moyu.chuanqirensheng.ui.theme.padding1
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingLargePlus
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.skill.Skill
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

@Composable
fun HeroDetailDialog(show: MutableState<Skill?>) {
    show.value?.let { skill ->
        CommonDialog(title = "",
            frame = null,
            noPadding = true,
            heightInDp = eventCardBigHeight * 2.1f,
            clickFrame = { show.value = null },
            onDismissRequest = {
                show.value = null
            }) {
            Column(
                Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                if (!repo.inGame.value) {
                    CurrentDiamondPoint(
                        Modifier
                            .align(Alignment.End)
                            .padding(end = paddingMedium),
                        showPlus = true,
                        showFrame = true
                    )
                } else {
                    Spacer(modifier = Modifier.size(moneyWidth, moneyHeight))
                }
                Spacer(modifier = Modifier.size(paddingSmall))
                ShadowedView(cardSize = CardSize.Large) {
                    SingleDetailHeroCard(
                        Modifier.size(eventCardBigWidth, eventCardBigHeight),
                        skill
                    )
                }
                HeroPropView(skill = skill)
                if (skill.canStarUp() && !repo.gameMode.value.isPvp() && !repo.gameMode.value.isTower()) {
                    Spacer(modifier = Modifier.size(paddingSmallPlus))
                    HeroStarUpView(skill = skill)
                } else if (repo.inGame.value && !skill.effected) {
                    Spacer(modifier = Modifier.size(paddingSmallPlus))
                    HeroEffectView(skill = skill)
                }
            }
        }
    }
}

@Composable
fun SingleDetailHeroCard(modifier: Modifier, skill: Skill) {
    Box(modifier.size(eventCardBigWidth, eventCardBigHeight)) {
        Image(
            modifier = Modifier
                .fillMaxSize()
                .padding(bottom = paddingSmallPlus)
                .clip(RoundedCornerShape(eventCardBigWidth / 22)),
            contentScale = ContentScale.Crop,
            alignment = Alignment.TopCenter,
            painter = painterResource(id = getImageResourceDrawable(skill.icon)),
            contentDescription = skill.name
        )
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = skill.getCardFrameDrawable()),
            contentDescription = null
        )
        HeroTitleView(
            Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = paddingLarge), skill
        )
        HeroTypeView(
            Modifier
                .align(Alignment.TopStart)
                .graphicsLayer {
                    translationX = -paddingLargePlus.toPx()
                    translationY = -paddingLargePlus.toPx()
                    clip = false
                }, skill
        )
    }
}

@Composable
fun HeroTitleView(modifier: Modifier, skill: Skill) {
    Column(
        modifier = modifier,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = skill.name,
            style = MaterialTheme.typography.h1,
            textAlign = TextAlign.Center
        )
        Spacer(modifier = Modifier.size(paddingSmall))
        Stars(
            stars = skill.level,
            starHeight = cardStarBigSize
        )
    }
}

@Composable
fun HeroTypeView(modifier: Modifier, skill: Skill) {
    EffectButton(modifier = modifier.size(imageLargePlus), onClick = {
        (skill.elementType.getHeroElementName()).toast()
    }) {
        Image(
            modifier = Modifier.fillMaxSize(),
            painter = painterResource(id = R.drawable.card_type),
            contentDescription = skill.name
        )
        Image(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingLarge)
                .graphicsLayer {
                    translationX = -padding1.toPx()
                    translationY = -padding1.toPx()
                },
            painter = painterResource(skill.getElementTypeRes()),
            contentDescription = skill.name
        )
    }
}

@Composable
fun HeroPropView(modifier: Modifier = Modifier, skill: Skill) {
    Box(
        modifier = modifier.height(eventCardBigHeight / 2.6f)
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = R.drawable.common_big_frame),
            contentDescription = null
        )
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(vertical = paddingSmall, horizontal = paddingMedium),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceEvenly
        ) {
            Row {
                Text(text = "【${skill.getTypeName()}】", style = MaterialTheme.typography.h3)
                if (skill.empire() != "0") {
                    Text(text = "【${skill.empire()}】", style = MaterialTheme.typography.h3)
                }
            }
            Spacer(modifier = Modifier.size(paddingSmall))
            Text(
                modifier = Modifier
                    .align(Alignment.Start)
                    .verticalScroll(rememberScrollState()),
                text = skill.getRealDescColorful(),
                style = MaterialTheme.typography.h4,
            )
            Spacer(modifier = Modifier.size(paddingSmall))
            Text(
                modifier = Modifier.align(Alignment.Start).verticalScroll(rememberScrollState()),
                text = skill.story(),
                style = MaterialTheme.typography.h5,
                color = W50,
                fontFamily = FontFamily.Monospace,
                fontStyle = FontStyle.Italic,
            )
        }
    }
}

@Composable
fun HeroStarUpView(modifier: Modifier = Modifier, skill: Skill) {
    val scroll = repo.gameCore.getHeroById(skill.id)
    if (skill.canStarUp()) {
        Box(
            modifier = modifier.height(eventCardBigHeight / 2.6f)
        ) {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(id = R.drawable.common_big_frame),
                contentDescription = null
            )
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingMedium),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.SpaceAround
            ) {
                val nextDesc = skill.getNextLevelSkill()
                val nextLevelString = stringResource(R.string.next_level)
                nextDesc?.let {
                    Text(
                        modifier = Modifier.align(Alignment.Start),
                        text = "$nextLevelString：${nextDesc.getRealDescColorful()}",
                        color = getSkillDescColor(true),
                        style = MaterialTheme.typography.h5
                    )
                }
                Spacer(modifier = Modifier.size(paddingSmall))
                val haveNextLevel = scroll.star < scroll.starLimit
                // 升星逻辑改了，有基底概念，所以要-1
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        text = stringResource(R.string.same_card) + (skill.num - 1) + "/" + scroll.starUpNum,
                        style = MaterialTheme.typography.h3
                    )
                    Spacer(modifier = Modifier.size(paddingSmall))
                    DiamondPoint(cost = scroll.starUpResourceNum, color = Color.White)
                }
                GameButton(text = if (haveNextLevel) stringResource(R.string.star_up) else stringResource(
                    R.string.star_max
                ),
                    buttonStyle = ButtonStyle.Orange,
                    enabled = !skill.peek && scroll.starUpNum != 0 && (skill.num - 1) >= scroll.starUpNum && scroll.star != scroll.starLimit && AwardManager.diamond.value >= scroll.starUpResourceNum,
                    onClick = {
                        if (skill.peek) {
                            GameApp.instance.getWrapString(R.string.peek_tips).toast()
                        } else {
                            restartEffect(newTurnEffectState, starUpEffect)
                            GameApp.globalScope.launch(gameDispatcher) {
                                Dialogs.heroDetailDialog.value = repo.heroManager.upgrade(skill)
                            }
                        }
                    })
            }
            EffectButton(modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(
                    paddingSmall
                ), onClick = {
                Dialogs.skillLevelInfoDialog.value = skill
            }) {
                Image(
                    modifier = Modifier.size(imageSmallPlus),
                    painter = painterResource(id = R.drawable.common_information),
                    contentDescription = stringResource(id = R.string.level_info)
                )
            }
        }
    }
}


@Composable
fun HeroEffectView(modifier: Modifier = Modifier, skill: Skill) {
    Box(
        modifier = modifier.height(eventCardBigHeight / 2.6f)
    ) {
        Image(
            modifier = Modifier.fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            painter = painterResource(id = R.drawable.common_big_frame),
            contentDescription = null
        )
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingMedium),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceAround
        ) {

            GameButton(text = stringResource(R.string.effect),
                buttonStyle = ButtonStyle.Orange,
                onClick = {
                    GameApp.globalScope.launch(Dispatchers.Main) {
                        if (!skill.effected) {
                            Dialogs.heroDetailDialog.value = BattleManager.heroEffect(skill)
                        }
                    }
                })
        }
    }
}
