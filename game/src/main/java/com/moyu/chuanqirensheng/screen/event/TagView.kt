package com.moyu.chuanqirensheng.screen.event

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.ui.theme.tagWidth


@Composable
fun TagView(modifier: Modifier = Modifier, text: String) {
    Box(modifier = modifier, contentAlignment = Alignment.Center) {
        Image(
            modifier = Modifier.width(tagWidth),
            painter = painterResource(id = R.drawable.common_label),
            contentDescription = null
        )
        Text(
            modifier = Modifier.padding(bottom = paddingSmallPlus, end = paddingSmall),
            text = text,
            maxLines = 1,
            style = if (text.length >= 5) MaterialTheme.typography.h4 else MaterialTheme.typography.h3
        )
    }
}