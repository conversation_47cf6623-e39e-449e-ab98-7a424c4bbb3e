package com.moyu.chuanqirensheng.screen.common

import androidx.activity.compose.LocalOnBackPressedDispatcherOwner
import androidx.compose.animation.ContentTransform
import androidx.compose.animation.ExperimentalAnimationApi
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.slideInVertically
import androidx.compose.animation.slideOutVertically
import androidx.compose.animation.with
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.ui.theme.backIconHeight
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.titleHeight
import com.moyu.chuanqirensheng.util.immersionBarHeightInDp

@Composable
fun GameBackground(
    gapStatusBar: Boolean = true,
    background: Int? = null,
    title: String = "",
    extraIcon: Int? = null,
    extraIconClick: (() -> Unit)? = null,
    bgMask: Color? = null,
    showCloseIcon: Boolean = true,
    showPreviewIcon: Boolean = true,
    content: @Composable BoxScope.() -> Unit = {}
) {
    Box(modifier = Modifier.fillMaxSize()) {
        background?.let {
            Image(
                modifier = Modifier.fillMaxSize(),
                contentScale = ContentScale.Crop,
                colorFilter = if (bgMask != null) ColorFilter.tint(
                    bgMask, BlendMode.SrcAtop
                ) else null,
                painter = painterResource(background),
                contentDescription = null
            )
        }
        if (showPreviewIcon || showCloseIcon) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(titleHeight + immersionBarHeightInDp),
                contentAlignment = Alignment.Center
            ) {
                Image(
                    modifier = Modifier.fillMaxSize(),
                    painter = painterResource(id = R.drawable.event_top_frame),
                    contentScale = ContentScale.FillBounds,
                    contentDescription = null
                )
                Row(Modifier.padding(top = immersionBarHeightInDp), verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        modifier = Modifier,
                        text = title,
                        style = MaterialTheme.typography.h1,
                    )
                    extraIcon?.let {
                        EffectButton(modifier = Modifier
                            .padding(start = paddingSmall)
                            .size(imageSmallPlus), onClick = {
                            extraIconClick?.invoke()
                        }) {
                            Image(painter = painterResource(id = it), contentDescription = null,)
                        }
                    }
                }
                if (showCloseIcon) {
                    LocalOnBackPressedDispatcherOwner.current?.onBackPressedDispatcher?.let {
                        TitleCloseButton(
                            Modifier
                                .align(Alignment.CenterEnd)
                                .padding(top = immersionBarHeightInDp)
                        ) {
                            it.onBackPressed()
                        }
                    }
                }
            }

        }
        val gap =
            (if (gapStatusBar) immersionBarHeightInDp else 0.dp) + (if (showPreviewIcon || showCloseIcon) titleHeight else 0.dp)
        Column {
            Spacer(Modifier.height(gap))
            Box(content = content)
        }
    }
}

@Composable
fun TitleCloseButton(
    modifier: Modifier = Modifier,
    onBackPressed:()->Unit
) {
    val pressing = remember {
        mutableStateOf(false)
    }
    EffectButton(modifier = modifier, pressing = pressing, onClick = {
        onBackPressed()
    }) {
        Image(
            modifier = Modifier.height(backIconHeight),
            contentScale = ContentScale.FillHeight,
            painter = painterResource(id = R.drawable.common_exit),
            contentDescription = stringResource(R.string.quit_page)
        )
    }
}

@ExperimentalAnimationApi
fun addAnimationVertical(duration: Int = 400): ContentTransform {
    return slideInVertically(animationSpec = tween(durationMillis = duration)) { height -> height } + fadeIn(
        animationSpec = tween(durationMillis = duration)
    ) with slideOutVertically(animationSpec = tween(durationMillis = duration)) { height -> height } + fadeOut(
        animationSpec = tween(durationMillis = duration)
    )
}