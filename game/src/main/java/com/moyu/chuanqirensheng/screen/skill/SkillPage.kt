package com.moyu.chuanqirensheng.screen.skill

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.equip.SearchView
import com.moyu.chuanqirensheng.screen.filter.*
import com.moyu.chuanqirensheng.ui.theme.gapSmallPlus
import com.moyu.chuanqirensheng.ui.theme.paddingHuge
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.core.model.skill.Skill

@Composable
fun SkillPage() {
    val search = remember {
        mutableStateOf("")
    }
    val showFilter = remember {
        mutableStateOf(false)
    }
    val filter = remember {
        mutableStateListOf<ItemFilter<Skill>>()
    }
    val showOrder = remember {
        mutableStateOf(false)
    }
    val order = remember {
        mutableStateOf(skillOrderList.first())
    }
    val list = repo.skillManager.data.filter {
        if (search.value.isNotEmpty()) {
            it.name.contains(search.value)
        } else true
    }.filter { ally ->
        filter.all { it.filter.invoke(ally) }
    }.sortedBy { order.value.order?.invoke(it) }

    DisposableEffect(Unit) {
        onDispose {
            repo.skillManager.setUnNew()
        }
    }
    Box(
        modifier = Modifier
            .fillMaxSize()
            .paint(
                painterResource(id = R.drawable.common_page_frame),
                contentScale = ContentScale.FillBounds
            ),
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize().padding(bottom = paddingMedium)
                .verticalScroll(rememberScrollState())
        ) {
            Spacer(modifier = Modifier.size(paddingMedium))
            Row(
                Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                CommonOrderView(
                    Modifier.padding(start = paddingMedium), showOrder
                )
                SearchView(search)
                CommonFilterView(
                    Modifier.padding(end = paddingMedium), showFilter
                )
            }
            LazyVerticalGrid(modifier = Modifier
                .weight(1f)
                .fillMaxWidth(),
                columns = GridCells.Fixed(4),
                content = {
                    items(list.size) { index ->
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            Spacer(modifier = Modifier.size(paddingMedium))
                            val skill = list[index]
                            SingleSkillView(
                                skill = skill,
                                showName = true,
                                showRed = true,
                                itemSize = ItemSize.LargePlus,
                                textColor = Color.White
                            )
                            Spacer(modifier = Modifier.size(paddingMedium))
                        }
                    }
                })
        }
        OrderLayout(
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(top = gapSmallPlus, end = paddingMedium),
            show = showOrder,
            filter = order,
            filterList = skillOrderList
        )
        FilterLayout(
            modifier = Modifier
                .align(Alignment.TopEnd)
                .padding(top = gapSmallPlus, end = paddingMedium),
            show = showFilter,
            filter = filter,
            filterList = skillFilterList
        )
    }
}