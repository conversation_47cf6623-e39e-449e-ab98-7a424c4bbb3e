package com.moyu.chuanqirensheng.screen.quest

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.task.TaskManager
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.NavigationTab


val questListTabItems = mutableStateOf(
        listOf(
            GameApp.instance.getWrapString(R.string.routine_quest),
            GameApp.instance.getWrapString(R.string.new_quest),
            GameApp.instance.getWrapString(R.string.one_time_quest),
        )
    )

@Composable
fun QuestAllScreen() {
    val questPagerState = rememberPagerState{
        questListTabItems.value.size
    }
    GameBackground(title = stringResource(R.string.quest)) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            NavigationTab(questPagerState, questListTabItems.value, listOf(TaskManager.dailyTasks.any {
                TaskManager.getTaskDoneFlow(it)
                        && !it.opened
            }, TaskManager.newTasks.any {
                TaskManager.getTaskDoneFlow(it)
                        && !it.opened
            }, TaskManager.oneTimeTasks.any {
                TaskManager.getTaskDoneFlow(it)
                        && !it.opened
            }))
            HorizontalPager(
                state = questPagerState,
            ) { page ->
                when (page) {
                    0 -> QuestScreen()
                    1 -> NewQuestScreen()
                    else -> OneTimeQuestScreen()
                }
            }
        }
    }
}