package com.moyu.chuanqirensheng.screen.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.battlepass.BattlePassManager
import com.moyu.chuanqirensheng.logic.task.TaskManager
import com.moyu.chuanqirensheng.logic.unlock.UNLOCK_WAR_PASS2
import com.moyu.chuanqirensheng.logic.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.ui.theme.imageTinyPlus
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.util.WARPASS2_SCREEN
import com.moyu.chuanqirensheng.util.goto

@Composable
fun WarPass2Icon(modifier: Modifier = Modifier) {
    val unlock = repo.gameCore.getUnlockById(UNLOCK_WAR_PASS2)
    if (UnlockManager.getUnlockedFlow(unlock)) {
        Column(horizontalAlignment = Alignment.CenterHorizontally, modifier = modifier) {
            EffectButton(modifier = Modifier.graphicsLayer {
                translationY = paddingMediumPlus.toPx()
            }, onClick = {
                goto(WARPASS2_SCREEN)
            }) {
                Image(
                    modifier = Modifier
                        .size(ItemSize.LargePlus.itemSize),
                    painter = painterResource(id = R.drawable.battle_pass2),
                    contentDescription = null
                )
                if (BattlePassManager.hasRed() || TaskManager.warPass2Tasks.any {
                        TaskManager.getTaskDoneFlow(it) && !it.opened
                    }) {
                    Image(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .padding(ItemSize.LargePlus.itemSize / 20)
                            .size(imageTinyPlus),
                        painter = painterResource(R.drawable.red_icon),
                        contentDescription = null
                    )
                }
            }
            Spacer(modifier = Modifier.size(paddingSmall))
            Text(
                text = stringResource(R.string.war_pass2), style = MaterialTheme.typography.h5,
                textAlign = TextAlign.Center, maxLines = 2
            )
        }
    }
}
