@file:OptIn(ExperimentalLayoutApi::class)

package com.moyu.chuanqirensheng.screen.event

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.story.StoryManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.SettingColumn
import com.moyu.chuanqirensheng.screen.common.SettingRow
import com.moyu.chuanqirensheng.screen.common.settingItems
import com.moyu.chuanqirensheng.screen.common.settingRowItems
import com.moyu.chuanqirensheng.screen.role.CountryInfoView
import com.moyu.chuanqirensheng.ui.theme.eventTopLayoutHeight
import com.moyu.chuanqirensheng.ui.theme.paddingHuge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.settingBgHeight
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.sell.Award
import com.moyu.core.model.sell.EMPTY_BADGE
import kotlinx.coroutines.launch

@Composable
fun EventSelectScreen() {
    Box {
        Column(
            modifier = Modifier.fillMaxSize()
        ) { // 左侧空出一些，避开setting
            CountryInfoView(
                Modifier
                    .fillMaxWidth()
                    .height(eventTopLayoutHeight)
            )
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .graphicsLayer {
                        translationY = -paddingMedium.toPx()
                    }, contentAlignment = Alignment.Center
            ) {
                EventLabelView()
            }
            if (DebugManager.debug) {
                FlowRow(modifier = Modifier.fillMaxWidth(),overflow = FlowRowOverflow.Visible) {
                    GameButton(buttonSize = ButtonSize.Small, text = "资源1000") {
                        GameApp.globalScope.launch {
                            repeat(5) {
                                BattleManager.gainElement(it, 1000)
                            }
                        }
                    }
                    GameButton(buttonSize = ButtonSize.Small, text = "产量1000") {
                        GameApp.globalScope.launch {
                            repeat(5) {
                                BattleManager.gainExtraElement(it, 1000)
                            }
                        }
                    }
                    GameButton(buttonSize = ButtonSize.Small, text = "特产1000") {
                        GameApp.globalScope.launch {
                            repeat(EMPTY_BADGE.size) {
                                BattleManager.gainBadge(it, 1000)
                            }
                        }
                    }
                    repeat(StoryManager.stories.size) {
                        GameButton(buttonSize = ButtonSize.Small, text = "${StoryManager.stories[it].name} 1000") {
                            GameApp.globalScope.launch {
                                    BattleManager.gainReputation(it, 1000)
                                }
                            }
                        }
                    GameButton(
                        text = "年龄+1",
                        buttonSize = ButtonSize.Small,
                    ) {
                        EventManager.gotoNextEvent(EventManager.selectedEvent.value, true)
                    }
                    GameButton(
                        text = "年龄+5",
                        buttonSize = ButtonSize.Small,
                    ) {
                        EventManager.gotoNextEvent(EventManager.selectedEvent.value, true, 5)
                    }
                    GameButton(
                        text = "年龄+50",
                        buttonSize = ButtonSize.Small,
                    ) {
                        EventManager.gotoNextEvent(EventManager.selectedEvent.value, true, 50)
                    }
                    GameButton(buttonSize = ButtonSize.Small, text = "国家10") {
                        GameApp.globalScope.launch {
                            BattleManager.gainAdventureProp(
                                AdventureProps(
                                    science = 10,
                                    politics = 10,
                                    military = 10,
                                    religion = 10,
                                    commerce = 10,
                                    art = 10,
                                    population = 10
                                )
                            )
                        }
                    }
                    GameButton(buttonSize = ButtonSize.Small, text = "国家100") {
                        GameApp.globalScope.launch {
                            BattleManager.gainAdventureProp(
                                AdventureProps(
                                    science = 100,
                                    politics = 100,
                                    military = 100,
                                    religion = 100,
                                    commerce = 100,
                                    art = 100,
                                    population = 100
                                )
                            )
                        }
                    }
                    GameButton(buttonSize = ButtonSize.Small, text = "冒险技能") {
                        Dialogs.debugAdvSkillDialog.value = {
                            GameApp.globalScope.launch {
                                AwardManager.gainAward(Award(skills = listOf(it)))
                            }
                        }
                    }
                }
            }
            EventSelectPage()
        }
        val switch = remember {
            mutableStateOf(false)
        }
        LaunchedEffect(BattleManager.adventureProps.value.age) {
            switch.value = true
        }
        SettingRow(
            Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = paddingSmall), settingRowItems
        )
        SettingColumn(
            Modifier
                .align(Alignment.BottomStart)
                .padding(bottom = settingBgHeight + paddingHuge),
            settingItems
        )
    }
}
