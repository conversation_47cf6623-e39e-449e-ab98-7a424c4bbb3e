package com.moyu.chuanqirensheng.screen.about

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.story.StoryManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.TitleCloseButton
import com.moyu.chuanqirensheng.screen.login.GameLogo
import com.moyu.chuanqirensheng.screen.login.TopItemsLeft
import com.moyu.chuanqirensheng.ui.theme.B35
import com.moyu.chuanqirensheng.ui.theme.buttonHeight
import com.moyu.chuanqirensheng.ui.theme.buttonWidth
import com.moyu.chuanqirensheng.ui.theme.dialogFrameHeight
import com.moyu.chuanqirensheng.ui.theme.gapMedium
import com.moyu.chuanqirensheng.ui.theme.paddingHuge
import com.moyu.chuanqirensheng.ui.theme.paddingMediumMinis
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.util.capture.ScreenshotBox
import com.moyu.chuanqirensheng.util.capture.rememberScreenshotState
import com.moyu.chuanqirensheng.util.immersionBarHeightInDp
import com.moyu.chuanqirensheng.util.pixelToDp
import com.moyu.chuanqirensheng.util.screenWidthInPixel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


private data class Item(val picID: Int, val desc: String)

private val listShowItems = listOf(
    Item(R.drawable.skill_6000, GameApp.instance.getWrapString(R.string.share_item1)),
    Item(R.drawable.skill_6001, GameApp.instance.getWrapString(R.string.share_item2)),
    Item(R.drawable.skill_6002, GameApp.instance.getWrapString(R.string.share_item3)),
    Item(R.drawable.skill_6003, GameApp.instance.getWrapString(R.string.share_item4)),
    Item(R.drawable.skill_6004, GameApp.instance.getWrapString(R.string.share_item5)),
    Item(R.drawable.skill_6005, GameApp.instance.getWrapString(R.string.share_item6))
)

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun UserShareGameScreen() {
    val capture = remember {
        mutableStateOf(false)
    }
    val screenshotState = rememberScreenshotState()
    ScreenshotBox(screenshotState = screenshotState) {
        Box(modifier = Modifier.fillMaxSize()) {
            Image(
                painter = painterResource(id = R.drawable.event_1000463),
                contentDescription = null,
                contentScale = ContentScale.Crop,
                modifier = Modifier.fillMaxSize()
            )
            TitleCloseButton(
                Modifier
                    .align(Alignment.TopEnd)
                    .padding(top = immersionBarHeightInDp)
            ) {
                GameApp.instance.activity.onBackPressed()
            }
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .background(B35),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.SpaceBetween
            ) {
                Spacer(modifier = Modifier.size(gapMedium))
                GameLogo()
                if (!capture.value) {
                    GameButton(text = stringResource(R.string.capture_share), onClick = {
                        capture.value = true
                        GameApp.globalScope.launch {
                            delay(100)
                            screenshotState.capture()
                            delay(500)
                            capture.value = false
                        }
                    })
                } else {
                    Spacer(modifier = Modifier.size(buttonWidth, buttonHeight))
                }
                LaunchedEffect(screenshotState.bitmap) {
                    if (screenshotState.bitmap != null) {
                        Dialogs.shareImageDialog.value = screenshotState.bitmap
                    }
                }
                // 中间6个框框 显示
                FlowRow(
                    maxItemsInEachRow = 3,
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceAround,
                    overflow = FlowRowOverflow.Visible,
                ) {
                    listShowItems.forEach {
                        Column(
                            modifier = Modifier.width(
                                (screenWidthInPixel.pixelToDp() / 3).minus(
                                    paddingMediumPlus
                                )
                            ), horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Box(modifier = Modifier.fillMaxWidth()) {
                                Image(
                                    modifier = Modifier.fillMaxWidth(),
                                    painter = painterResource(id = R.drawable.item_quality_3),
                                    contentScale = ContentScale.FillWidth,
                                    contentDescription = null
                                )
                                Image(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(paddingMediumPlus)
                                        .clip(RoundedCornerShape(paddingSmall)),
                                    painter = painterResource(id = it.picID),
                                    contentScale = ContentScale.FillWidth,
                                    contentDescription = null
                                )
                            }
                            Box(contentAlignment = Alignment.Center) {
                                Image(
                                    painter = painterResource(id = R.drawable.scroll),
                                    contentScale = ContentScale.FillWidth,
                                    contentDescription = null
                                )
                                Text(
                                    text = it.desc,
                                    style = MaterialTheme.typography.h5,
                                    color = Color.Black,
                                    textAlign = TextAlign.Center
                                )
                            }
                        }
                    }
                }
                // 底部
                Column(
                    modifier = Modifier.padding(horizontal = paddingHuge)
                ) {
                    Row {
                        TopItemsLeft()
                    }
                    Box(modifier = Modifier
                        .fillMaxWidth()
                        .height(dialogFrameHeight)) {
                        Image(
                            painter = painterResource(id = R.drawable.dialog_frame),
                            contentDescription = null,
                            contentScale = ContentScale.FillWidth,
                            modifier = Modifier.fillMaxWidth()
                        )
                        val text = StoryManager.endings.maxByOrNull { it.age }?.endingText
                            ?: stringResource(R.string.share_tips)
                        Text(
                            text = text,
                            style = MaterialTheme.typography.h4,
                            color = Color.Black,
                            modifier = Modifier
                                .padding(horizontal = paddingHuge, vertical = paddingMediumMinis)
                                .verticalScroll(rememberScrollState())
                        )
                    }
                }
            }
        }
    }
}
