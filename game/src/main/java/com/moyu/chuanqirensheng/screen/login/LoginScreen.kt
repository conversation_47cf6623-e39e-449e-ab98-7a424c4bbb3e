package com.moyu.chuanqirensheng.screen.login

import androidx.compose.foundation.horizontalScroll
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.cloud.privacy.PrivacyManager
import com.moyu.chuanqirensheng.datastore.KEY_DIED_IN_GAME
import com.moyu.chuanqirensheng.datastore.KEY_NEW_USER
import com.moyu.chuanqirensheng.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.datastore.setStringValueByKey
import com.moyu.chuanqirensheng.debug.DebugButton
import com.moyu.chuanqirensheng.feature.draw.ui.DrawIcon
import com.moyu.chuanqirensheng.feature.gift.ui.ErrorOrder
import com.moyu.chuanqirensheng.feature.holiday.ui.HolidayIcon
import com.moyu.chuanqirensheng.feature.lottery.ui.LotteryIcon
import com.moyu.chuanqirensheng.feature.mode.EndlessGameMode
import com.moyu.chuanqirensheng.feature.newTask.ui.SevenDayIcon
import com.moyu.chuanqirensheng.feature.pvp.ui.PvpIcon
import com.moyu.chuanqirensheng.feature.sign.ui.SignIcon
import com.moyu.chuanqirensheng.feature.tower.ui.TowerIcon
import com.moyu.chuanqirensheng.logic.guide.GuideManager
import com.moyu.chuanqirensheng.logic.task.TaskManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.antiaddic.AntiAddictDialog
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.ui.theme.gapHuge
import com.moyu.chuanqirensheng.ui.theme.gapLarge
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding360
import com.moyu.chuanqirensheng.ui.theme.padding80
import com.moyu.chuanqirensheng.ui.theme.paddingLarge

@Composable
fun LoginScreen() {
    LaunchedEffect(Unit) {
        repo.inGame.value = false
        setStringValueByKey(KEY_NEW_USER, "false")
        if (!PrivacyManager.privacyNeedShow && !PrivacyManager.permissionNeedShow) {
            GameApp.instance.initSDK(GameApp.instance.activity)
        }
        GuideManager.showFirstGuide()
        // 保证能及时刷新
        TaskManager.createTasks()
        // 只要回到首页，就清除局内死亡次数（用来做局内触发礼包）
        setIntValueByKey(KEY_DIED_IN_GAME, 0)
        // 只要回到首页就切到无尽模式
        repo.gameMode.value = EndlessGameMode()
    }

    GameBackground(
        showCloseIcon = false,
        showPreviewIcon = false,
    ) {
        Column(Modifier.fillMaxSize(), horizontalAlignment = Alignment.CenterHorizontally) {
            Spacer(modifier = Modifier.size(gapLarge))
            GameLogo()
            Spacer(modifier = Modifier.size(gapHuge))
            GameMenu(Modifier.weight(1f))
            Row(
                Modifier
                    .heightIn(padding0, padding360)
                    .fillMaxWidth()
                    .horizontalScroll(rememberScrollState()),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                Spacer(Modifier.size(padding22))
                ErrorOrder()
                DrawIcon()
                PvpIcon()
                TowerIcon()
                LotteryIcon()
                FirstChargeIcon()
                SevenDayIcon()
                SignIcon()
                WarPassIcon()
                WarPass2Icon()
                Spacer(Modifier.size(padding22))
            }
            Spacer(modifier = Modifier.size(paddingLarge))
        }
        TopItemsLeft(Modifier.align(Alignment.TopStart))
        TopItemsRight(Modifier.align(Alignment.TopEnd))
        HolidayIcon(Modifier.align(Alignment.CenterStart).padding(start = padding12).graphicsLayer {
            translationY = -padding80.toPx()
        })
        DebugButton(Modifier.align(Alignment.Center))
    }
    AntiAddictDialog {
        if (GameApp.instance.hasLogin()) {
            GameApp.instance.checkAntiAddiction(GameApp.instance.activity)
        } else {
            GameApp.instance.login(GameApp.instance.activity)
        }
    }
}