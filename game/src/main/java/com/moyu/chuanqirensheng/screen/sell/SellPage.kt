package com.moyu.chuanqirensheng.screen.sell

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.sell.SellManager
import com.moyu.chuanqirensheng.logic.sell.SellManager.items
import com.moyu.chuanqirensheng.logic.sell.preCondition
import com.moyu.chuanqirensheng.screen.common.CurrentDiamondPoint
import com.moyu.chuanqirensheng.screen.common.CurrentKeyPoint
import com.moyu.chuanqirensheng.screen.common.CurrentPvpPoint
import com.moyu.chuanqirensheng.screen.common.GameLabel
import com.moyu.chuanqirensheng.ui.theme.gapLarge
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding8


@Composable
fun SellPage(itemTypes: List<Int>, showPvpDiamond: Boolean = false) {
    LaunchedEffect(Unit) {
        SellManager.init()
    }
    val shopChests =
        items.filter { it.type in itemTypes && it.preCondition() }
            .sortedBy { it.order }.filter {
            // storageType是永久限量，storage是剩余数量
            it.storage > 0 || it.storageType != 2
        }.filter {
            // 钥匙商店，如果是谷歌商店，不显示没有谷歌商品id的商品
            if (it.isAifadian()) {
                it.googleItemId != "0" || !GameApp.instance.resources.getBoolean(R.bool.has_google_service)
            } else true
        }
    Column(
        Modifier.paint(
            painterResource(id = R.drawable.common_big_frame),
            contentScale = ContentScale.FillBounds
        )
    ) {
        Spacer(modifier = Modifier.size(padding8))
        Row(
            modifier = Modifier
                .padding(start = padding14)
        ) {
            if (showPvpDiamond) {
                CurrentPvpPoint(
                    showPlus = true,
                    showFrame = true
                )
            } else {
                CurrentKeyPoint(
                    showPlus = true,
                    showFrame = true
                )
                CurrentDiamondPoint(
                    showPlus = false,
                    showFrame = true
                )
            }
        }
        Column(
            modifier = Modifier
                .fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(vertical = padding19)
                    .verticalScroll(
                        rememberScrollState()
                    ),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                shopChests.groupBy { it.type }.entries.sortedBy { it.key }.forEach { typeGroups ->
                    typeGroups.value.groupBy { it.title }.keys.forEach { title ->
                        val items = shopChests.filter { it.type == typeGroups.key }
                            .filter { it.title == title }
                        if (title.isNotEmpty() && title != "0") {
                            GameLabel {
                                Text(
                                    text = title,
                                    style = MaterialTheme.typography.h2,
                                    textAlign = TextAlign.Center,
                                    color = Color.Black
                                )
                            }
                        }
                        FlowRow(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceEvenly,
                            overflow = FlowRowOverflow.Visible,
                            maxItemsInEachRow = 3
                        ) {
                            items
                                .sortedBy { it.priceType }
                                .forEach {
                                    OneSellItem(it)
                                }
                        }
                    }
                }
                Spacer(modifier = Modifier.size(gapLarge))
            }
        }
    }
}