package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.RepeatMode
import androidx.compose.animation.core.animateFloat
import androidx.compose.animation.core.infiniteRepeatable
import androidx.compose.animation.core.rememberInfiniteTransition
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.zIndex
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.award.toBadgeTip
import com.moyu.chuanqirensheng.logic.award.toElementIcon
import com.moyu.chuanqirensheng.logic.award.toElementName
import com.moyu.chuanqirensheng.logic.award.toExtraElementIcon
import com.moyu.chuanqirensheng.logic.award.toExtraElementName
import com.moyu.chuanqirensheng.logic.hero.getQualityFrame
import com.moyu.chuanqirensheng.logic.skill.getFrameDrawable
import com.moyu.chuanqirensheng.logic.story.StoryManager
import com.moyu.chuanqirensheng.logic.story.toReputationName
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.common.getTextStyle
import com.moyu.chuanqirensheng.screen.equip.DropPropertyLine
import com.moyu.chuanqirensheng.screen.more.Stars
import com.moyu.chuanqirensheng.screen.skill.MaskView
import com.moyu.chuanqirensheng.sub.language.LanguageManager
import com.moyu.chuanqirensheng.ui.theme.DARK_RED
import com.moyu.chuanqirensheng.ui.theme.dialogWidth
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingTiny
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.ally.Ally
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.sell.Award
import com.moyu.core.model.sell.EMPTY_BADGE
import com.moyu.core.model.sell.EMPTY_ELEMENTS
import com.moyu.core.model.sell.EMPTY_REPUTATION
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isBattleSkill
import com.moyu.core.model.skill.quality
import kotlin.math.roundToInt

@Composable
fun AwardDialog(show: MutableState<Award?>) {
    show.value?.let {
        val title = if (it.property.isPunish()) stringResource(R.string.punish) else stringResource(
            R.string.awards
        )
        val button =
            if (it.property.isPunish()) stringResource(R.string.confirm) else stringResource(
                id = R.string.gain
            )
        CommonDialog(title = title, onDismissRequest = {
            show.value = null
        }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .verticalScroll(rememberScrollState()),
                horizontalAlignment = Alignment.CenterHorizontally,
            ) {
                AwardList(award = it)
                Spacer(modifier = Modifier.weight(1f))
                GameButton(text = button, buttonStyle = ButtonStyle.Orange, onClick = {
                    show.value = null
                })
            }
            ForeverGif(
                Modifier
                    .width(dialogWidth), "reward_", 16
            )
        }
    }
}

@Composable
fun ForeverGif(modifier: Modifier,
               resource: String,
               num: Int,
               translateY: Dp = padding0,
               scale: Float = 1f,
               needGap: Boolean = false,
               contentScale: ContentScale = ContentScale.FillWidth) {
    val infiniteTransition = rememberInfiniteTransition(label = "")
    val index = infiniteTransition.animateFloat(
        initialValue = 1f, targetValue = if (needGap) num * 6f else num.toFloat(), animationSpec = infiniteRepeatable(
            animation = tween(if (needGap) num * 6 * 70 else num * 70, easing = LinearEasing),
            repeatMode = RepeatMode.Restart,
        ), label = ""
    )
    if (index.value.roundToInt() <= num) { // 做一个间歇的效果
        Image(
            modifier = modifier
                .graphicsLayer {
                    translationY = translateY.toPx()
                }.scale(scale),
            contentScale = contentScale,
            painter = painterResource(
                getImageResourceDrawable(
                    "${resource}${index.value.roundToInt()}"
                )
            ),
            contentDescription = null
        )
    }
}

data class AwardUIParam(
    val showName: Boolean = true,
    val itemSize: ItemSize = ItemSize.LargePlus,
    val frameDrawable: Int? = R.drawable.item_quality_3,
    val numInFrame: Boolean = true,
    val frameZIndex: Float = 0f,
    val peek: Boolean = false,
    val checkAffordable: Boolean = false,
    val showColumn: Boolean = true,
    val textColor: Color = Color.Black,
    val showEffect: Boolean = false,
    val showReputationLevel: Boolean = false,
    val minLine: Int = 2,
    val maxLine: Int = 2,
    val callback: (() -> Unit)? = null,
)

val defaultParam = AwardUIParam()

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun AwardList(
    modifier: Modifier = Modifier,
    award: Award,
    mainAxisAlignment: Arrangement.Horizontal = Arrangement.Start,
    param: AwardUIParam = defaultParam,
) {
    FlowRow(
        modifier = modifier.padding(horizontal = paddingMedium, vertical = paddingMedium),
        horizontalArrangement = mainAxisAlignment,
        overflow = FlowRowOverflow.Visible,
    ) {
        award.outAllies.filter { it.quality == 3 }.forEach {
            OutAllyAwardItem(it, award, param)
        }
        award.outHeroes.filter { it.quality() == 3 }.forEach {
            OutHeroAwardItem(it, award, param)
        }
        award.outSkills.filter { it.quality() == 3 }.groupBy { it.id }.forEach {
            val skill = it.value.first()
            val size = it.value.sumOf { it.num }
            OutSkillAwardItem(skill, award, param, size)
        }
        award.outAllies.filter { it.quality == 2 }.forEach {
            OutAllyAwardItem(it, award, param)
        }
        award.outHeroes.filter { it.quality() == 2 }.forEach {
            OutHeroAwardItem(it, award, param)
        }
        award.outSkills.filter { it.quality() == 2 }.groupBy { it.id }.forEach {
            val skill = it.value.first()
            val size = it.value.sumOf { it.num }
            OutSkillAwardItem(skill, award, param, size)
        }
        award.outAllies.filter { it.quality == 1 }.forEach {
            OutAllyAwardItem(it, award, param)
        }
        award.outHeroes.filter { it.quality() == 1 }.forEach {
            OutHeroAwardItem(it, award, param)
        }
        award.outSkills.filter { it.quality() == 1 }.groupBy { it.id }.forEach {
            val skill = it.value.first()
            val size = it.value.sumOf { it.num }
            OutSkillAwardItem(skill, award, param, size)
        }

        award.elements.forEachIndexed { index, i ->
            if (i != 0) {
                SingleAwardItem(
                    name = (index).toElementName(),
                    drawable = (index).toElementIcon(),
                    num = if (i > 0) "+$i" else "$i",
                    param = param,
                ) {
                    AwardManager.isAffordable(
                        Award(
                            elements = EMPTY_ELEMENTS.toMutableList().apply {
                                set(index, i)
                            })
                    )
                }
            }
        }
        award.extraElements.forEachIndexed { index, i ->
            if (i != 0) {
                SingleAwardItem(
                    name = (index).toExtraElementName(),
                    drawable = (index).toExtraElementIcon(),
                    num = if (i > 0) "+$i" else "$i",
                    param = param,
                )
            }
        }
        award.pvpDiamond.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(id = R.string.pvp_diamond),
                drawable = R.drawable.pvp_diamond_icon,
                num = "+$it",
                param = param.copy(callback = param.callback?: {
                    GameApp.instance.getWrapString(R.string.pvp_diamond_tips).toast()
                })
            )
        }
        award.pvpScore.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(id = R.string.pvp_score),
                drawable = R.drawable.pvp_icon,
                num = "+$it",
                param = param.copy(callback = param.callback?: {
                    GameApp.instance.getWrapString(R.string.pvp_score_tips).toast()
                })
            )
        }
        award.adMoney.takeIf { it > 0 }?.let {
            SingleAwardItem(
                stringResource(id = R.string.ad_money),
                R.drawable.ad_value_icon,
                "+${it}",
                param = param
            )
        }
        award.badges.takeIf { it.any { it != 0 } }?.let {
            it.forEachIndexed { index, i ->
                if (i != 0) {
                    val badge = repo.gameCore.getBadgePool().first { it.id == index + 1 }
                    SingleAwardItem(
                        drawable = if (award.showQuestion) R.drawable.common_question else getImageResourceDrawable(
                            badge.pic
                        ),
                        name = if (award.showQuestion) stringResource(R.string.item) else badge.name,
                        num = "+$i",
                        param = param.copy(frameDrawable = 3.getQualityFrame(), callback = param.callback?: {
                            (index + 1).toBadgeTip().toast()
                        }),
                    ) {
                        AwardManager.isAffordable(Award(badges = EMPTY_BADGE.toMutableList().apply {
                            set(index, i)
                        }))
                    }
                }
            }
        }
        award.reputations.takeIf { it.any { it != 0 } }?.let {
            it.forEachIndexed { index, i ->
                if (i != 0) {
                    SingleAwardItem(
                        drawable = StoryManager.getImageByIndex(index),
                        name = (index + 1).toReputationName() ?: "",
                        num = if (param.showReputationLevel) AdventureProps.getReputationLevelData(i).name else "+$i",
                        param = param.copy(frameDrawable = 2.getQualityFrame()),
                    ) {
                        AwardManager.isAffordable(
                            Award(
                                reputations = EMPTY_REPUTATION.toMutableList().apply {
                                    set(index, i)
                                })
                        )
                    }
                }
            }
        }
        award.key.takeIf { it > 0 }?.let {
            if (award.extraDiamond > 0) {
                SingleAwardItem(
                    name = stringResource(id = R.string.key_title),
                    drawable = R.drawable.common_key,
                    num = "+${it - award.extraKey}(${award.extraKey})",
                    param = param.copy(frameDrawable = 3.getQualityFrame(), callback = param.callback?: {
                        GameApp.instance.getWrapString(R.string.key_tips).toast()
                    }),
                )
            } else {
                SingleAwardItem(
                    name = stringResource(id = R.string.key_title),
                    drawable = R.drawable.common_key,
                    num = "+$it",
                    param = param.copy(frameDrawable = 3.getQualityFrame(), callback = param.callback?: {
                        GameApp.instance.getWrapString(R.string.key_tips).toast()
                    }),
                ) {
                    AwardManager.isAffordable(Award(key = it))
                }
            }
        }
        award.diamond.takeIf { it > 0 }?.let {
            if (award.extraDiamond > 0) {
                SingleAwardItem(
                    name = stringResource(id = R.string.diamond_title),
                    drawable = R.drawable.common_medal,
                    num = "+${it - award.extraDiamond}(${award.extraDiamond})",
                    param = param.copy(frameDrawable = 3.getQualityFrame(), callback = param.callback?: {
                        GameApp.instance.getWrapString(R.string.diamond_tips).toast()
                    }),
                )
            } else SingleAwardItem(
                name = stringResource(id = R.string.diamond_title),
                drawable = R.drawable.common_medal,
                num = "+$it",
                param = param.copy(frameDrawable = 3.getQualityFrame(), callback = param.callback?: {
                    GameApp.instance.getWrapString(R.string.diamond_tips).toast()
                }),
            ) {
                AwardManager.isAffordable(Award(diamond = it))
            }
        }
        award.lotteryMoney.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(id = R.string.lottery_money),
                drawable = R.drawable.lottery_money,
                num = "+$it",
                param = param.copy(callback = param.callback?: {
                    GameApp.instance.getWrapString(R.string.lottery_money_tips).toast()
                })
            )
        }
        award.holidayMoney.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(id = R.string.holiday_money),
                drawable = R.drawable.holiday_money,
                num = "+$it",
                param = param.copy(callback = param.callback?: {
                    GameApp.instance.getWrapString(R.string.holiday_money_tips).toast()
                })
            )
        }
        award.warPass.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(id = R.string.war_pass_exp),
                drawable = R.drawable.item_battlepass_exp,
                num = "+$it",
                param = param.copy(frameDrawable = 3.getQualityFrame(), callback = param.callback?: {
                    GameApp.instance.getWrapString(R.string.war_pass_tips).toast()
                }),
            )
        }
        award.warPass2.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(id = R.string.war_pass2_exp),
                drawable = R.drawable.battle_pass2,
                num = "+$it",
                param = param.copy(frameDrawable = 3.getQualityFrame(), callback = param.callback?: {
                    GameApp.instance.getWrapString(R.string.war_pass2_tips).toast()
                }),
            )
        }
        award.exp.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(id = R.string.exp),
                drawable = R.drawable.common_exp,
                num = "+$it",
                param = param,
            )
        }
        award.property.takeIf { it.isNotEmpty() }?.DropPropertyLine(param = param)
        award.heroes.forEach {
            SingleAwardItem(
                name = it.name,
                drawable = getImageResourceDrawable(it.icon),
                stars = if (it.isBattleSkill()) it.level else -1,
                contentScale = if (award.showQuestion) ContentScale.Fit else ContentScale.Crop,
                alignment = Alignment.TopCenter,
                param = param.copy(frameDrawable = it.getFrameDrawable(), callback = {
                    Dialogs.heroDetailDialog.value = it.copy(peek = true)
                }),
            )
        }
        award.loseHeroes.forEach {
            SingleAwardItem(
                name = it.name,
                num = "-1",
                drawable = getImageResourceDrawable(it.icon),
                stars = if (it.isBattleSkill()) it.level else -1,
                contentScale = if (award.showQuestion) ContentScale.Fit else ContentScale.Crop,
                alignment = Alignment.TopCenter,
                param = param.copy(frameDrawable = it.getFrameDrawable(), callback = {
                    Dialogs.heroDetailDialog.value = it.copy(peek = true)
                }),
            )
        }
        award.skills.forEach {
            SingleAwardItem(
                name = it.name,
                drawable = getImageResourceDrawable(it.icon),
                stars = if (it.isBattleSkill()) it.level else -1,
                contentScale = if (award.showQuestion) ContentScale.Fit else ContentScale.Crop,
                param = param.copy(frameDrawable = it.getFrameDrawable(), callback = {
                    Dialogs.skillDetailDialog.value = it.copy(peek = true)
                }),
            )
        }
        award.loseSkills.forEach {
            SingleAwardItem(
                name = it.name,
                num = "-1",
                drawable = getImageResourceDrawable(it.icon),
                stars = if (it.isBattleSkill()) it.level else -1,
                contentScale = if (award.showQuestion) ContentScale.Fit else ContentScale.Crop,
                param = param.copy(frameDrawable = it.getFrameDrawable(), callback = {
                    Dialogs.skillDetailDialog.value = it.copy(peek = true)
                }),
            )
        }
        award.allies.forEach {
            val race = repo.gameCore.getRaceById(it.id)
            SingleAwardItem(
                name = it.name,
                drawable = getImageResourceDrawable(race.pic),
                stars = it.star,
                contentScale = if (award.showQuestion) ContentScale.Fit else ContentScale.Crop,
                alignment = Alignment.TopCenter,
                param = param.copy(frameDrawable = it.quality.getQualityFrame(), callback = {
                    Dialogs.allyDetailDialog.value = it.copy(peek = true)
                }),
            )
        }
        award.loseAllies.forEach {
            val race = repo.gameCore.getRaceById(it.id)
            SingleAwardItem(
                name = it.name,
                num = "-1",
                drawable = getImageResourceDrawable(race.pic),
                stars = it.star,
                contentScale = if (award.showQuestion) ContentScale.Fit else ContentScale.Crop,
                alignment = Alignment.TopCenter,
                param = param.copy(frameDrawable = it.quality.getQualityFrame(), callback = {
                    Dialogs.allyDetailDialog.value = it.copy(peek = true)
                }),
            )
        }
        award.tcgs.forEach {
            SingleAwardItem(
                name = if (award.showQuestion) stringResource(id = R.string.random_tcg_card) else it.name,
                drawable = if (award.showQuestion) R.drawable.common_question else getImageResourceDrawable(
                    imgName = it.pic
                ),
                param = param.copy(frameDrawable = it.quality.getQualityFrame(), callback = {
                    if (award.showQuestion) {
                        GameApp.instance.getWrapString(R.string.random_tcg_card).toast()
                    } else {
                        Dialogs.tcgCardDialog.value = it
                    }
                })
            )
        }
        award.skins.forEach {
            SingleAwardItem(
                it.name,
                getImageResourceDrawable(imgName = it.pic),
                alignment = Alignment.TopCenter,
                contentScale = ContentScale.Crop,
                param = param.copy(callback = {
                    Dialogs.skinDialog.value = it
                })
            )
        }
        award.unlockList.forEach {
            val unlock = repo.gameCore.getUnlockById(it)
            SingleAwardItem(
                name = unlock.name,
                drawable = getImageResourceDrawable(unlock.icon),
                contentScale = ContentScale.Crop,
                param = param.copy(frameDrawable = 3.getQualityFrame()),
            )
        }
        award.electric.takeIf { it > 0 }?.let {
            SingleAwardItem(
                name = stringResource(id = R.string.electric_title),
                drawable = R.drawable.common_charge,
                num = "+$it",
                param = param.copy(frameDrawable = 3.getQualityFrame()),
            )
        }
    }
}

@Composable
fun OutSkillAwardItem(skill: Skill, award: Award, param: AwardUIParam, size: Int) {
    if (award.showQuestion) {
        SingleAwardItem(
            name = stringResource(id = R.string.random_battle_skill),
            drawable = R.drawable.random_skill_icon,
            num = if (size == 1 && repo.inGame.value) stringResource(R.string.forever) else "x${size}",
            stars = if (skill.isBattleSkill()) skill.level else -1,
            contentScale = if (award.showQuestion) ContentScale.Fit else ContentScale.Crop,
            param = param.copy(
                showEffect = skill.quality() >= 3,
                frameDrawable = skill.getFrameDrawable(),
                callback = {
                    param.callback?.invoke() ?: run {
                        GameApp.instance.getWrapString(R.string.random_battle_skill).toast()
                    }
                }),
        )
    } else {
        SingleAwardItem(
            name = skill.name,
            drawable = getImageResourceDrawable(skill.icon),
            num = if (size == 1 && repo.inGame.value) stringResource(R.string.forever) else "x${size}",
            stars = if (skill.isBattleSkill()) skill.level else -1,
            contentScale = if (award.showQuestion) ContentScale.Fit else ContentScale.Crop,
            param = param.copy(
                showEffect = skill.quality() >= 3,
                frameDrawable = skill.getFrameDrawable(),
                callback = {
                    param.callback?.invoke() ?: run {
                        Dialogs.skillDetailDialog.value = skill.copy(peek = true)
                    }
                }),
        )
    }
}

@Composable
fun OutHeroAwardItem(it: Skill, award: Award, param: AwardUIParam) {
    if (award.showQuestion) {
        SingleAwardItem(
            name = stringResource(id = R.string.random_hero_card),
            drawable = R.drawable.random_hero_icon,
            num = if (it.num == 1 && repo.inGame.value) stringResource(R.string.forever) else "x${it.num}",
            stars = if (it.isBattleSkill()) it.level else -1,
            contentScale = if (award.showQuestion) ContentScale.Fit else ContentScale.Crop,
            alignment = Alignment.TopCenter,
            param = param.copy(
                showEffect = it.quality() >= 3,
                frameDrawable = it.getFrameDrawable(),
                callback = {
                    param.callback?.invoke() ?: run {
                        GameApp.instance.getWrapString(R.string.random_hero_card).toast()
                    }
                }),
        )
    } else {
        SingleAwardItem(
            name = it.name,
            drawable = getImageResourceDrawable(it.icon),
            num = if (it.num == 1 && repo.inGame.value) stringResource(R.string.forever) else "x${it.num}",
            stars = if (it.isBattleSkill()) it.level else -1,
            contentScale = if (award.showQuestion) ContentScale.Fit else ContentScale.Crop,
            alignment = Alignment.TopCenter,
            param = param.copy(
                showEffect = it.quality() >= 3,
                frameDrawable = it.getFrameDrawable(),
                callback = {
                    param.callback?.invoke() ?: run {
                        Dialogs.heroDetailDialog.value = it.copy(peek = true)
                    }
                }),
        )
    }
}

@Composable
fun OutAllyAwardItem(it: Ally, award: Award, param: AwardUIParam) {
    val race = repo.gameCore.getRaceById(it.id)
    if (award.showQuestion) {
        SingleAwardItem(
            name = stringResource(id = R.string.random_ally_card),
            drawable = R.drawable.random_ally_icon,
            num = if (it.num == 1 && repo.inGame.value) stringResource(R.string.forever) else "x${it.num}",
            stars = it.star,
            contentScale = if (award.showQuestion) ContentScale.Fit else ContentScale.Crop,
            alignment = Alignment.TopCenter,
            param = param.copy(
                showEffect = it.quality >= 3,
                frameDrawable = it.quality.getQualityFrame(),
                callback = {
                    param.callback?.invoke() ?: run {
                        GameApp.instance.getWrapString(R.string.random_ally_card).toast()
                    }
                }),
        )
    } else {
        SingleAwardItem(
            name = it.name,
            drawable = getImageResourceDrawable(race.pic),
            num = if (it.num == 1 && repo.inGame.value) stringResource(R.string.forever) else "x${it.num}",
            stars = it.star,
            contentScale = if (award.showQuestion) ContentScale.Fit else ContentScale.Crop,
            alignment = Alignment.TopCenter,
            param = param.copy(
                showEffect = it.quality >= 3,
                frameDrawable = it.quality.getQualityFrame(),
                callback = {
                    param.callback?.invoke() ?: run {
                        Dialogs.allyDetailDialog.value = it.copy(peek = true)
                    }
                }),
        )
    }
}

@Composable
fun SingleAwardItem(
    name: String,
    drawable: Int,
    num: String = "",
    stars: Int = -1,
    contentScale: ContentScale = ContentScale.Fit,
    alignment: Alignment = Alignment.Center,
    param: AwardUIParam = defaultParam,
    affordable: () -> Boolean = { true },
) {
    if (param.showColumn) {
        Column(
            modifier = Modifier
                .width(param.itemSize.frameSize + paddingTiny)
                .padding(vertical = paddingSmall),
            horizontalAlignment = Alignment.CenterHorizontally,
        ) {
            InnerAwardItem(
                name = name,
                drawable = drawable,
                num = num,
                stars = stars,
                contentScale = contentScale,
                alignment = alignment,
                param = param,
                affordable = affordable
            )
        }
    } else {
        Row(
            modifier = Modifier
                .padding(horizontal = paddingTiny),
            verticalAlignment = Alignment.CenterVertically,
        ) {
            InnerAwardItem(
                name = name,
                drawable = drawable,
                num = num,
                stars = stars,
                contentScale = contentScale,
                alignment = alignment,
                param = param,
                affordable = affordable
            )
        }
    }
}

@Composable
fun InnerAwardItem(
    name: String,
    drawable: Int,
    num: String = "",
    stars: Int = -1,
    contentScale: ContentScale = ContentScale.Fit,
    alignment: Alignment = Alignment.Center,
    param: AwardUIParam = defaultParam,
    affordable: () -> Boolean = { true },
) {
    val realNum = if (param.checkAffordable) {
        // 如果是条件展示，不需要+号
        num.replace("+", " ")
    } else if (num.contains("-")) {
        // 如果是扣减，去掉+号
        num.replace("+", "")
    } else num
    EffectButton(onClick = {
        param.callback?.invoke() ?: run { name.toast() }
    }) {
        param.frameDrawable?.let {
            Image(
                modifier = Modifier
                    .size(param.itemSize.frameSize)
                    .zIndex(param.frameZIndex),
                contentScale = ContentScale.FillBounds,
                painter = painterResource(it),
                contentDescription = null
            )
        }
        Image(
            modifier = Modifier
                .size(param.itemSize.itemSize)
                .clip(RoundedCornerShape(paddingSmall)),
            alignment = alignment,
            contentScale = contentScale,
            painter = painterResource(drawable),
            contentDescription = name
        )
        if (realNum.isNotEmpty() && param.numInFrame) {
            MaskView(
                modifier = Modifier.align(Alignment.BottomCenter),
                text = realNum,
                itemSize = param.itemSize
            )
        } else if (stars != -1) {
            Stars(
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = paddingSmall),
                stars = stars,
                starHeight = param.itemSize.itemSize / 5
            )
        }
        if (param.showEffect) {
            val infiniteTransition = rememberInfiniteTransition(label = "")
            val index = infiniteTransition.animateFloat(
                initialValue = 1f, targetValue = 50f, animationSpec = infiniteRepeatable(
                    animation = tween(1600, easing = LinearEasing),
                    repeatMode = RepeatMode.Restart,
                ), label = ""
            )
            if (index.value.roundToInt() <= 15) { // 做一个间歇的效果
                Image(
                    modifier = Modifier
                        .size(param.itemSize.frameSize)
                        .scale(1.3f),
                    painter = painterResource(
                        getImageResourceDrawable(
                            "rewarditem_${index.value.roundToInt()}"
                        )
                    ),
                    contentDescription = null
                )
            }
        }
    }
    Spacer(modifier = Modifier.height(paddingTiny))
    // 还是要控制，中文就只1行显示，不然太丑了
    if (param.showName) {
        Text(
            text = if (param.numInFrame) name else "$name$realNum",
            style = param.itemSize.getTextStyle(),
            maxLines = if (LanguageManager.isSelectedChinese()) 1 else param.maxLine,
            minLines = if (LanguageManager.isSelectedChinese()) 1 else param.minLine,
            softWrap = !LanguageManager.isSelectedChinese(),
            color = param.textColor,
            overflow = TextOverflow.Visible,
            textAlign = TextAlign.Center
        )
    } else {
        if (!param.numInFrame) {
            Text(
                text = realNum,
                style = param.itemSize.getTextStyle(),
                maxLines = if (LanguageManager.isSelectedChinese()) 1 else param.maxLine,
                minLines = if (LanguageManager.isSelectedChinese()) 1 else param.minLine,
                color = if (param.checkAffordable && !affordable()) DARK_RED else param.textColor,
                softWrap = !LanguageManager.isSelectedChinese(),
                overflow = TextOverflow.Visible,
                textAlign = TextAlign.Center
            )
        }
    }
}