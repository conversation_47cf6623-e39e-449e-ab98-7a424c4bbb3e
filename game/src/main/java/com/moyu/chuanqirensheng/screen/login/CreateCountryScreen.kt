package com.moyu.chuanqirensheng.screen.login

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager.heroManualEffect
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.ally.InitAllyCardsView
import com.moyu.chuanqirensheng.screen.ally.MAX_ALLY_SKILL_SIZE
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.skill.InitHeroCardView
import com.moyu.chuanqirensheng.screen.skill.InitSkillCardView
import com.moyu.chuanqirensheng.ui.theme.createCountryLabelHeight
import com.moyu.chuanqirensheng.ui.theme.createCountryPanelHeight
import com.moyu.chuanqirensheng.ui.theme.imageSmallPlus
import com.moyu.chuanqirensheng.ui.theme.padding200
import com.moyu.chuanqirensheng.ui.theme.padding7
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.ui.theme.paddingTiny
import com.moyu.chuanqirensheng.util.STORY_SCREEN
import com.moyu.chuanqirensheng.util.goto
import com.moyu.core.GameCore
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


@Composable
fun CreateCountryScreen() {
    GameBackground(title = stringResource(R.string.create_country)) {
        Column(
            Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.SpaceEvenly
        ) {
            CreateCountryNameLayout()
            InitAllyCardLayout()
            InitSkillCardLayout()
            InitHeroCardLayout()
            CreateCountryButtonLayout()
        }
    }
}

@Composable
fun CreateCountryButtonLayout() {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        GameButton(
            text = stringResource(R.string.change_story),
            buttonStyle = ButtonStyle.Blue,
            buttonSize = ButtonSize.Big
        ) {
            goto(STORY_SCREEN)
        }
        GameButton(
            text = stringResource(R.string.start_game),
            buttonStyle = ButtonStyle.Orange,
            buttonSize = ButtonSize.Big,
            onClick = {
                if (BattleManager.getGameAllies().isEmpty()) {
                    GameApp.instance.getWrapString(R.string.ally_tips).toast()
                } else if (BattleManager.getGameAllies().size < 3) {
                    Dialogs.alertDialog.value =
                        CommonAlert(
                            content = GameApp.instance.getWrapString(R.string.ally_tips_contents),
                            onConfirm = {
                                repo.startGame(false)
                                GameCore.instance.onBattleEffect(SoundEffect.StartGame)
                            })
                } else {
                    repo.startGame(false)
                    GameCore.instance.onBattleEffect(SoundEffect.StartGame)
                }
            })
    }
}

@Composable
fun InitHeroCardLayout() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .fillMaxWidth()
            .height(createCountryPanelHeight)
            .paint(
                painterResource(id = R.drawable.common_big_frame),
                contentScale = ContentScale.FillBounds
            )
    ) {
        Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceBetween) {
            Column {
                Spacer(modifier = Modifier.size(paddingSmallPlus))
                Text(
                    text = stringResource(R.string.choose_person),
                    style = MaterialTheme.typography.h3,
                    modifier = Modifier
                        .align(Alignment.Start)
                        .padding(start = padding7)
                )
                Spacer(modifier = Modifier.size(paddingTiny))
            }
            EffectButton(
                modifier = Modifier.padding(top = paddingTiny, end = paddingMediumPlus),
                onClick = {
                    heroManualEffect.value = !heroManualEffect.value
                }) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Image(
                        modifier = Modifier.size(imageSmallPlus),
                        painter = painterResource(id = if (!heroManualEffect.value) R.drawable.setup_2 else R.drawable.setup_1),
                        contentDescription = if (!heroManualEffect.value) stringResource(R.string.game_selected) else stringResource(
                            R.string.game_not_selected
                        )
                    )
                    Spacer(modifier = Modifier.size(paddingMedium))
                    Text(
                        text = stringResource(R.string.auto_effect),
                        style = MaterialTheme.typography.h5
                    )
                }
            }
        }
        InitHeroCardView(
            modifier = Modifier.fillMaxWidth()
        )
        Spacer(modifier = Modifier.size(paddingSmallPlus))
    }
}

@Composable
fun InitSkillCardLayout() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .fillMaxWidth()
            .height(createCountryPanelHeight)
            .paint(
                painterResource(id = R.drawable.common_big_frame),
                contentScale = ContentScale.FillBounds
            )
    ) {
        Spacer(modifier = Modifier.size(paddingSmallPlus))
        Text(
            text = stringResource(R.string.choose_tips),
            style = MaterialTheme.typography.h3,
            modifier = Modifier
                .align(Alignment.Start)
                .padding(start = padding7)
        )
        Spacer(modifier = Modifier.size(paddingTiny))
        InitSkillCardView(modifier = Modifier.fillMaxWidth())
        Spacer(modifier = Modifier.size(paddingSmallPlus))
    }
}

@Composable
fun InitAllyCardLayout() {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .fillMaxWidth()
            .height(createCountryPanelHeight)
            .paint(
                painterResource(id = R.drawable.common_big_frame),
                contentScale = ContentScale.FillBounds
            )
    ) {
        Spacer(modifier = Modifier.size(paddingSmallPlus))
        Text(
            text = stringResource(R.string.choose_tips2), style = MaterialTheme.typography.h3,
            modifier = Modifier
                .align(Alignment.Start)
                .padding(start = padding7)
        )
        Spacer(modifier = Modifier.size(paddingTiny))
        InitAllyCardsView(modifier = Modifier
            .fillMaxWidth(),
            allies = BattleManager.getGameAllies(),
            capacity = MAX_ALLY_SKILL_SIZE,
            allyClick = {
                GameApp.globalScope.launch(Dispatchers.Main) {
                    BattleManager.selectToGame(it)
                }
            }) {
            Dialogs.selectAllyToGameDialog.value = true
        }
        Spacer(modifier = Modifier.size(paddingSmallPlus))
    }
}

@Composable
fun CreateCountryNameLayout() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .height(createCountryLabelHeight)
            .paint(
                painterResource(id = R.drawable.common_big_frame),
                contentScale = ContentScale.FillBounds
            ),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = stringResource(R.string.random_name),
            style = MaterialTheme.typography.h3,
            modifier = Modifier
                .align(Alignment.TopStart)
                .padding(padding7)
                .padding(top = paddingMedium)
        )
        Image(
            painter = painterResource(id = R.drawable.scroll),
            contentDescription = null,
            modifier = Modifier
                .scale(1.2f)
                .graphicsLayer {
                    translationY = paddingMedium.toPx()
                }
        )
        Text(
            text = BattleManager.countryName.value,
            color = Color.Black,
            style = MaterialTheme.typography.h2,
            textAlign = TextAlign.Center,
            modifier = Modifier
                .width(padding200)
                .align(Alignment.Center)
                .graphicsLayer {
                    translationY = paddingMedium.toPx()
                }
        )
        DiceView(modifier = Modifier.align(Alignment.CenterEnd)) {
            BattleManager.createRandomCountryName()
        }
    }
}
