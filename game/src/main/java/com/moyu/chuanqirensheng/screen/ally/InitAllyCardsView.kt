@file:OptIn(ExperimentalLayoutApi::class)

package com.moyu.chuanqirensheng.screen.ally

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.skill.EmptyIconView
import com.moyu.chuanqirensheng.screen.skill.GifView
import com.moyu.chuanqirensheng.screen.skill.IconView
import com.moyu.chuanqirensheng.ui.theme.imageLarge
import com.moyu.chuanqirensheng.ui.theme.oneRoleWidth
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.ui.theme.paddingTiny
import com.moyu.chuanqirensheng.ui.theme.singleRoleHeight
import com.moyu.core.model.ally.Ally
import org.burnoutcrew.reorderable.ReorderableItem
import org.burnoutcrew.reorderable.detectReorderAfterLongPress
import org.burnoutcrew.reorderable.rememberReorderableLazyListState
import org.burnoutcrew.reorderable.reorderable

const val MAX_ALLY_SKILL_SIZE = 10
const val MAX_HERO_SIZE = 10

@Composable
fun InitAllyCardsView(
    modifier: Modifier = Modifier,
    allies: List<Ally>,
    capacity: Int,
    allyClick: (Ally) -> Unit = { Dialogs.allyDetailDialog.value = it },
    emptyClick: () -> Unit
) {
    FlowRow(
        modifier = modifier,
        horizontalArrangement = Arrangement.SpaceEvenly,
        overflow = FlowRowOverflow.Visible,
        maxItemsInEachRow = MAX_ALLY_SKILL_SIZE / 2,
    ) {
        var skillCount = 0
        repeat(capacity) { index ->
            val unlock = UnlockManager.getInitAllyUnlockByIndex(index)
            val unlocked = UnlockManager.getUnlockedFlow(unlock)
            Box(modifier = Modifier.padding(vertical = paddingTiny)) {
                val gif = remember {
                    mutableStateOf(false)
                }
                allies.getOrNull(skillCount)?.takeIf { unlocked }?.let { ally ->
                    skillCount += 1
                    Box(contentAlignment = Alignment.Center) {
                        SingleAllyView(
                            ally = ally,
                            extraInfo = ally.extraInfo,
                            showName = false,
                            itemSize = ItemSize.LargePlus,
                            selectCallBack = allyClick
                        )
                        GifView(modifier = Modifier.size(ItemSize.LargePlus.frameSize), gif.value, 8, "equipon_")
                    }
                } ?: run {
                    gif.value = true
                    Box(contentAlignment = Alignment.Center) {
                        EmptyIconView(itemSize = ItemSize.LargePlus) {
                            if (unlocked) {
                                emptyClick()
                            } else {
                                unlock.desc.toast()
                            }
                        }
                        if (!unlocked) {
                            Image(
                                modifier = Modifier.size(imageLarge),
                                painter = painterResource(R.drawable.common_lock),
                                contentDescription = stringResource(id = R.string.locked),
                            )
                        }
                    }
                }
            }
        }
    }
}


@Composable
fun AllyCardsRow(
    modifier: Modifier = Modifier,
    allies: List<Ally>,
    capacity: Int,
    showName: Boolean = false,
    showHp: Boolean = false,
    canDrag: Boolean = false,
    textColor: Color = Color.Black,
    allyClick: (Ally) -> Unit = { Dialogs.allyDetailDialog.value = it },
    emptyClick: () -> Unit
) {
    if (canDrag) {
        val state = rememberReorderableLazyListState(onMove = { from, to ->
            BattleManager.switchAllyBattlePosition(from.index, to.index)
        })
        LazyRow(
            state = state.listState,
            modifier = modifier
                .reorderable(state)
                .detectReorderAfterLongPress(state),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            items(capacity, { allies.getOrNull(it)?.uuid ?: it }) { item ->
                val allyNullable = allies.getOrNull(item)
                ReorderableItem(state, key = allyNullable?.uuid ?: item) { isDragging ->
                    Box(
                        modifier = Modifier
                            .size(oneRoleWidth, singleRoleHeight)
                            .scale(if (isDragging) 1.5f else 1f),
                        contentAlignment = Alignment.Center
                    ) {
                        allyNullable?.let { ally ->
                            Column(horizontalAlignment = Alignment.CenterHorizontally) {
                                Box(contentAlignment = Alignment.Center) {
                                    SingleAllyView(
                                        ally = ally,
                                        extraInfo = ally.extraInfo,
                                        showName = false,
                                        itemSize = ItemSize.LargePlus,
                                        textColor = textColor,
                                        showHp = showHp
                                    )
                                    IconView(
                                        Modifier
                                            .align(Alignment.TopEnd)
                                            .graphicsLayer {
                                                translationY = -paddingSmallPlus.toPx()
                                                translationX = paddingSmallPlus.toPx()
                                            },
                                        res = R.drawable.menu8_exit,
                                        itemSize = ItemSize.Medium,
                                        frame = null,
                                        name = stringResource(R.string.remove)
                                    ) {
                                        allyClick(ally)
                                    }
                                    GifView(modifier = Modifier.size(ItemSize.LargePlus.frameSize), true, 8, "equipon_")
                                }
                                if (showName) {
                                    Text(text = ally.name, style = MaterialTheme.typography.body1)
                                }
                            }
                        } ?: run {
                            Box(contentAlignment = Alignment.Center) {
                                EmptyIconView(itemSize = ItemSize.LargePlus) {
                                    emptyClick()
                                }
                            }
                        }
                    }
                }
            }
        }
    } else {
        Row(
            modifier = modifier, horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            repeat(capacity) { index ->
                Box(
                    modifier = Modifier.size(oneRoleWidth, singleRoleHeight),
                    contentAlignment = Alignment.Center
                ) {
                    allies.getOrNull(index)?.let { ally ->
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            Box(contentAlignment = Alignment.Center) {
                                SingleAllyView(
                                    ally = ally,
                                    extraInfo = ally.extraInfo,
                                    showName = false,
                                    textColor = textColor,
                                    itemSize = ItemSize.LargePlus,
                                    showHp = showHp
                                )
                                IconView(
                                    Modifier
                                        .align(Alignment.TopEnd)
                                        .graphicsLayer {
                                            translationY = -paddingSmallPlus.toPx()
                                            translationX = paddingSmallPlus.toPx()
                                        },
                                    res = R.drawable.menu8_exit,
                                    itemSize = ItemSize.Medium,
                                    frame = null,
                                    name = stringResource(R.string.remove)
                                ) {
                                    allyClick(ally)
                                }
                                GifView(modifier = Modifier.size(ItemSize.LargePlus.frameSize), true, 8, "equipon_")
                            }
                            if (showName) {
                                Text(text = ally.name, style = MaterialTheme.typography.body1)
                            }
                        }
                    } ?: run {
                        Box(contentAlignment = Alignment.Center) {
                            EmptyIconView(itemSize = ItemSize.LargePlus) {
                                emptyClick()
                            }
                        }
                    }
                }
            }
        }
    }
}