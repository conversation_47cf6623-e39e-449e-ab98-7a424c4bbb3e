package com.moyu.chuanqirensheng.screen.movie

import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import androidx.media3.common.MediaItem
import androidx.media3.common.util.UnstableApi
import androidx.media3.datasource.DefaultDataSource
import androidx.media3.datasource.RawResourceDataSource
import androidx.media3.exoplayer.ExoPlayer
import androidx.media3.exoplayer.source.ProgressiveMediaSource
import androidx.media3.ui.AspectRatioFrameLayout
import androidx.media3.ui.PlayerView
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp


@UnstableApi
object VideoPlayerManager {
    val mExoPlayer = mutableStateOf<ExoPlayer?>(null)

    fun init() {
        try {
            if (mExoPlayer.value == null) {
                val mContext = GameApp.instance
                mExoPlayer.value = ExoPlayer.Builder(mContext).build()

                val uri = RawResourceDataSource.buildRawResourceUri(R.raw.game_bg)
                val dataSourceFactory = DefaultDataSource.Factory(mContext)
                val mediaSource = ProgressiveMediaSource.Factory(dataSourceFactory)
                    .createMediaSource(MediaItem.fromUri(uri))
                mExoPlayer.value?.setMediaSource(mediaSource)
                mExoPlayer.value?.prepare()
                mExoPlayer.value?.repeatMode = ExoPlayer.REPEAT_MODE_ONE
                mExoPlayer.value?.playWhenReady = true
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
}

@UnstableApi
@Composable
fun VideoPlayer2(modifier: Modifier) {
    if (VideoPlayerManager.mExoPlayer.value != null) {
        AndroidView(modifier = modifier, factory = { context ->
            PlayerView(context).apply {
                try {
                    useController = false
                    player = VideoPlayerManager.mExoPlayer.value
                    player = player
                    resizeMode = AspectRatioFrameLayout.RESIZE_MODE_ZOOM
                } catch (e: Exception) {
                    e.printStackTrace()
                }

            }
        })
    }
}