package com.moyu.chuanqirensheng.screen.life

import android.content.Intent
import android.net.Uri
import android.os.Bundle
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.core.content.ContextCompat
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.cloud.bill.BillingManager
import com.moyu.chuanqirensheng.logic.sell.SellManager
import com.moyu.chuanqirensheng.logic.sell.getPriceTextWithUnit
import com.moyu.chuanqirensheng.logic.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.ui.theme.imageHuge
import com.moyu.chuanqirensheng.ui.theme.imageHugeFramePlus
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import com.moyu.chuanqirensheng.ui.theme.paddingTiny
import com.moyu.chuanqirensheng.util.getImageResourceDrawable
import com.moyu.core.model.sell.toAward
import com.moyu.core.model.story.Story
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch


@Composable
fun SingleStorySell(modifier: Modifier = Modifier, story: Story) {
    val sell = repo.gameCore.getSellPool().first { it.id == story.sellId }
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceBetween,
        modifier = modifier
            .padding(horizontal = paddingMedium, vertical = paddingMedium)
    ) {
        EffectButton(onClick = {
            Dialogs.storyDetailDialog.value = story
        }) {
            Image(
                modifier = Modifier
                    .width(imageHugeFramePlus),
                contentScale = ContentScale.FillWidth,
                painter = painterResource(id = R.drawable.prestige_frame),
                contentDescription = null
            )
            Image(
                painter = painterResource(getImageResourceDrawable(story.pic)),
                modifier = Modifier
                    .size(imageHuge)
                    .graphicsLayer {
                        translationX = -paddingTiny.toPx()
                        translationY = -paddingSmallPlus.toPx()
                    },
                contentScale = ContentScale.FillWidth,
                contentDescription = null
            )
            Text(
                text = stringResource(id = R.string.check),
                style = MaterialTheme.typography.h5,
                maxLines = 1,
                overflow = TextOverflow.Visible,
                textAlign = TextAlign.Center
            )
        }
        Text(
            modifier = Modifier.graphicsLayer {
                translationY = -paddingMedium.toPx()
            },
            text = story.name,
            style = MaterialTheme.typography.h3,
            maxLines = 1,
            overflow = TextOverflow.Visible,
            textAlign = TextAlign.Center
        )
        val lock = repo.gameCore.getUnlockById(story.unlockId)
        val unlocked = UnlockManager.getUnlockedFlow(lock)
        GameButton(
            modifier = Modifier.graphicsLayer {
                translationY = -paddingLarge.toPx()
            },
            enabled = !unlocked,
            text = if (unlocked) stringResource(id = R.string.bought) else sell.getPriceTextWithUnit(),
            textColor = Color.White,
            buttonSize = ButtonSize.Medium,
            buttonStyle = ButtonStyle.Orange
        ) {
            if (!GameApp.instance.resources.getBoolean(R.bool.has_billing)) {
                if (GameApp.instance.canShowAifadian()) {
                    val uri: Uri = Uri.parse(lock.url)
                    val intent = Intent(Intent.ACTION_VIEW, uri)
                    ContextCompat.startActivity(
                        GameApp.instance.activity,
                        intent,
                        Bundle()
                    )
                } else {
                    GameApp.instance.getWrapString(R.string.cant_get_yet).toast()
                }
            } else if (unlocked) {
                GameApp.instance.getWrapString(R.string.bought).toast()
            } else {
                GameApp.globalScope.launch(Dispatchers.Main) {
                    BillingManager.prepay(
                        sell = sell) {
                        GameApp.globalScope.launch(Dispatchers.Main) {
                            SellManager.dealAfterPay(sell, sell.toAward())
                        }
                    }
                    Dialogs.aiFaDianDialog.value = null
                }
            }
        }
    }
}
