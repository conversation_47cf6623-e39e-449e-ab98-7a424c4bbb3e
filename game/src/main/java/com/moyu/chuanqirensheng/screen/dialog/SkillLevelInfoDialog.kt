package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.buildAnnotatedString
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.skill.getRealDescColorful
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.ui.theme.paddingLarge
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.core.model.skill.Skill

@Composable
fun SkillLevelInfoDialog(result: MutableState<Skill?>) {
    result.value?.let { skill ->
        val skills = repo.gameCore.getSkillPool().filter { it.mainId == skill.mainId }
        val currentLevel = skill.level
        CommonDialog(title = stringResource(R.string.level_info), onDismissRequest = {
            result.value = null
        }) {
            Column(
                Modifier
                    .fillMaxSize()
                    .padding(horizontal = paddingMedium)
            ) {
                Spacer(modifier = Modifier.size(paddingMedium))
                Column(
                    Modifier
                        .weight(1f)
                        .verticalScroll(rememberScrollState())
                ) {
                    skills.forEach {
                        Text(
                            text = buildAnnotatedString { append("Lv.${it.level}:") } + it.getRealDescColorful(
                                MaterialTheme.typography.h4.toSpanStyle()
                            ),
                            style = MaterialTheme.typography.h4,
                            color = if (it.level == currentLevel) Color.Black else Color.Gray
                        )
                        Spacer(modifier = Modifier.size(paddingMedium))
                    }
                    Spacer(modifier = Modifier.weight(1f))
                }
                GameButton(
                    modifier = Modifier.align(Alignment.CenterHorizontally),
                    text = stringResource(id = R.string.confirm),
                    buttonStyle = ButtonStyle.Orange
                ) {
                    result.value = null
                }
                Spacer(modifier = Modifier.size(paddingLarge))
            }
        }
    }
}