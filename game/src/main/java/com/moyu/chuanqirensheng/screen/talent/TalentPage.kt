package com.moyu.chuanqirensheng.screen.talent

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.graphics.BlendMode
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.talent.TalentManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.common.CurrentDiamondPoint
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.skill.FlipView
import com.moyu.chuanqirensheng.screen.skill.SingleSkillView
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.paddingLargePlus
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.ui.theme.paddingSmallPlus
import kotlin.math.max

@Composable
fun TalentPage(type: Int) {
    val allTalents = repo.gameCore.getTalentPool().filter { it.type == type }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .paint(
                painterResource(id = R.drawable.common_page_frame),
                contentScale = ContentScale.FillBounds
            )
            .verticalScroll(rememberScrollState()),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Spacer(modifier = Modifier.size(paddingSmallPlus))
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(horizontal = paddingMedium),
            horizontalArrangement = Arrangement.End,
            verticalAlignment = Alignment.CenterVertically
        ) {
            CurrentDiamondPoint(showPlus = true, showFrame = true)
        }
        Spacer(modifier = Modifier.size(paddingSmallPlus))
        Row(modifier = Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly) {
            repeat(3) { column ->
                Column(Modifier.weight(1f), horizontalAlignment = Alignment.CenterHorizontally) {
                    repeat(26) { row ->
                        allTalents.filter {
                            it.position.first() == row + 1
                                    && it.position[1] == column + 1
                        }.takeIf { it.isNotEmpty() }?.let { talents ->
                            val talentLevel = TalentManager.talents[talents.first().mainId] ?: 0
                            val showTalentLevel = max(1, talentLevel)
                            val showTalent = talents.first { it.level == showTalentLevel }
                            val locked = TalentManager.getLockInfoByTalent(showTalent).first
                            val skill = repo.gameCore.getSkillById(showTalent.talentSkill)
                            Spacer(modifier = Modifier.size(paddingSmall))
                            FlipView(Modifier, delay = (row * 3 + column + 3) * 100L, front = {
                                Image(
                                    modifier = Modifier.size(ItemSize.HugeTalent.frameSize),
                                    painter = painterResource(R.drawable.talent_frame),
                                    contentDescription = null,
                                )
                            }) {
                                SingleSkillView(
                                    skill = skill,
                                    showName = false,
                                    itemSize = ItemSize.HugeTalent,
                                    locked = locked,
                                    colorFilter = if (locked) ColorFilter.tint(
                                        B50, BlendMode.SrcAtop
                                    ) else null,
                                    showStars = false,
                                    frame = if (locked) R.drawable.talent_frame else R.drawable.talent_learned_new,
                                    imageClip = RoundedCornerShape(ItemSize.HugeTalent.itemSize / 2f)
                                ) {
                                    Dialogs.detailTalentDialog.value = showTalent
                                }
                            }

                            Text(text = "Lv.${talentLevel}/${showTalent.levelLimit}", style = MaterialTheme.typography.h3)
                            Text(
                                text = skill.name,
                                maxLines = 2,
                                minLines = 2,
                                style = MaterialTheme.typography.h3,
                                textAlign = TextAlign.Center
                            )
                            Spacer(modifier = Modifier.size(paddingLargePlus))
                        }
                    }
                }
            }
        }
    }
}