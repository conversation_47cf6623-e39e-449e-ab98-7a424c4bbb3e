package com.moyu.chuanqirensheng.feature.battlepass.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.paint
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import com.moyu.chuanqirensheng.BuildConfig
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.logic.task.TaskManager
import com.moyu.chuanqirensheng.screen.equip.SearchView
import com.moyu.chuanqirensheng.screen.quest.SingleQuest
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.chuanqirensheng.util.refreshNetTime
import com.moyu.core.model.task.GameTask

@Composable
fun WarPassQuestScreen() {
    val tasks = remember {
        mutableStateListOf<GameTask>()
    }
    val refresh = remember {
        mutableStateOf(0)
    }
    val search = remember {
        mutableStateOf("")
    }
    LaunchedEffect(refresh.value.toString() + search.value) {
        if (!isNetTimeValid()) {
            refreshNetTime()
        }
        TaskManager.createWarPassTasks()
        // 完成的任务排前面，已领取的排最后
        TaskManager.warPassTasks.map {
            it.copy(done = TaskManager.getTaskDoneFlow(it))
        }.sortedBy { it.order }
            .sortedByDescending { (if (it.done) 1000 else 0) + (if (it.opened) -5000 else 0) }
            .apply {
                tasks.clear()
                if (search.value.isNotEmpty()) {
                    tasks.addAll(this.filter { it.name.contains(search.value) })
                } else {
                    tasks.addAll(this)
                }
            }
    }
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        Spacer(modifier = Modifier.size(paddingSmall))
        if (BuildConfig.FLAVOR.contains("Lite")) {
            SearchView(search)
        }
        LazyColumn(modifier = Modifier
            .fillMaxSize()
            .paint(
                painterResource(id = R.drawable.common_page_frame),
                contentScale = ContentScale.FillBounds
            ), content = {
            items(tasks.size) { index ->
                Column(modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = paddingMedium)) {
                    SingleQuest(tasks[index]) {
                        refresh.value += 1
                    }
                    Spacer(modifier = Modifier.size(paddingSmall))
                }
            }
        })
    }
}