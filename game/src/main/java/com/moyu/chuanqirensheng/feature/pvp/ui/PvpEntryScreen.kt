package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.getLastDayRanks
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.json
import com.moyu.chuanqirensheng.feature.pvp.MAX_PVP_NUM
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.logic.award.VipManager
import com.moyu.chuanqirensheng.logic.rank.RankManager
import com.moyu.chuanqirensheng.logic.sell.SELL_TYPE_PVP
import com.moyu.chuanqirensheng.logic.sell.SellManager
import com.moyu.chuanqirensheng.logic.task.TaskManager
import com.moyu.chuanqirensheng.model.record.AllRankData
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.effect.ShadowImage
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.chuanqirensheng.ui.theme.W30
import com.moyu.chuanqirensheng.ui.theme.imageMedium
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding150
import com.moyu.chuanqirensheng.ui.theme.padding165
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding26
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.util.PVP_CHOOSE_ENEMY_SCREEN
import com.moyu.chuanqirensheng.util.PVP_QUEST_SCREEN
import com.moyu.chuanqirensheng.util.PVP_RANK_SCREEN
import com.moyu.chuanqirensheng.util.PVP_SELL_SCREEN
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.goto
import com.moyu.chuanqirensheng.util.isBetween23_45And00_15
import com.moyu.chuanqirensheng.util.isNetTimeValid
import kotlinx.coroutines.delay
import kotlinx.serialization.builtins.ListSerializer
import timber.log.Timber

@Composable
fun PvpEntryScreen() {
    LaunchedEffect(Unit) {
        // 进这个页面就需要刷一下排行榜，需要领取排名任务
        PvpManager.init()
        RankManager.init()
        try {
            if (!isBetween23_45And00_15(getCurrentTime())) {
                if (lastPvpRanks.value.isEmpty()) {
                    delay(200)
                    getLastDayRanks(
                        GameApp.instance.resources.getString(
                            R.string.platform_channel
                        )
                    ).let {
                        lastPvpRanks.value = json.decodeFromString(
                            ListSerializer(AllRankData.serializer()), it.message).filter { it.userId != GameApp.instance.getObjectId() }.filter {
                            it.userId !in PvpManager.pkTargetList
                        }
                    }
                }
            }
        } catch (e: Exception) {
            Timber.e(e)
            GameApp.instance.getWrapString(R.string.net_error_retry).toast()
        }
    }
    GameBackground(
        title = stringResource(R.string.pvp),
        bgMask = B50,
        background = R.drawable.bg_pvp
    ) {
        PvpTopDataRow(Modifier
            .fillMaxWidth()
            .padding(top = padding6)
            .background(W30))
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = padding12, vertical = padding100),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            SingleMapItem(
                Modifier
                    .weight(1f)
                    .align(Alignment.End)
                    .padding(end = padding22),
                stringResource(id = R.string.pvp_shop),
                R.drawable.pvp_shop_icon,
                red = {
                    SellManager.getRedFree(SELL_TYPE_PVP)
                }
            ) {
                goto(PVP_SELL_SCREEN)
            }
            SingleMapItem(
                Modifier
                    .weight(1f)
                    .align(Alignment.Start)
                    .padding(start = padding22),
                stringResource(R.string.pvp),
                R.drawable.pvp_entrance_icon,
                red = {
                    PvpManager.pkNumToday.value < MAX_PVP_NUM + VipManager.getExtraPvpNum()
                }
            ) {
                if (isBetween23_45And00_15(getCurrentTime())) {
                    GameApp.instance.getWrapString(R.string.arena_time_tips).toast()
                } else {
                    pvpRanks.value = emptyList()
                    goto(PVP_CHOOSE_ENEMY_SCREEN)
                }
            }
            SingleMapItem(
                Modifier
                    .weight(1f)
                    .align(Alignment.End)
                    .padding(end = padding0),
                stringResource(R.string.pvp_quest), R.drawable.pvp_quest_icon,
                red = {
                    TaskManager.pvpTasks.any {
                        TaskManager.getTaskDoneFlow(it)
                                && !it.opened
                    }
                }
            ) {
                if (isNetTimeValid()) {
                    if (isBetween23_45And00_15(getCurrentTime())) {
                        GameApp.instance.getWrapString(R.string.arena_time_tips).toast()
                    } else {
                        goto(PVP_QUEST_SCREEN)
                    }
                }
            }
            SingleMapItem(
                Modifier
                    .weight(1f)
                    .align(Alignment.Start)
                    .padding(start = padding3),
                stringResource(R.string.pvp_rank),
                R.drawable.pvp_rank_icon,
                red = {
                    false
                }
            ) {
                if (isBetween23_45And00_15(getCurrentTime())) {
                    GameApp.instance.getWrapString(R.string.arena_time_tips).toast()
                } else {
                    goto(PVP_RANK_SCREEN)
                }
            }
        }
    }
}

@Composable
fun SingleMapItem(modifier: Modifier, name: String, buildingRes: Int, red: ()-> Boolean, callback: () -> Unit) {
    Column(modifier = modifier, horizontalAlignment = Alignment.CenterHorizontally) {
        Box(contentAlignment = Alignment.Center) {
            EffectButton(onClick = {
                callback()
            }) {
                Box(
                    Modifier
                        .width(padding165 + padding12)
                        .height(padding165 + padding22),
                    contentAlignment = Alignment.TopCenter
                ) {
                    ShadowImage(
                        modifier = Modifier.size(padding150),
                        imageResource = buildingRes,
                    )
                    if (red()) {
                        Image(
                            modifier = Modifier
                                .align(Alignment.TopEnd)
                                .padding(padding26)
                                .size(imageMedium),
                            painter = painterResource(R.drawable.red_icon),
                            contentDescription = null
                        )
                    }
                }
                Box(Modifier.align(Alignment.BottomCenter), contentAlignment = Alignment.Center) {
                    Text(
                        text = name, style = MaterialTheme.typography.h1
                    )
                }
            }
        }
    }
}