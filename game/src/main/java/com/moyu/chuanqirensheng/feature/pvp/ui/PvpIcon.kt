package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.feature.pvp.MAX_PVP_NUM
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.logic.award.VipManager
import com.moyu.chuanqirensheng.logic.sell.SELL_TYPE_PVP
import com.moyu.chuanqirensheng.logic.sell.SellManager
import com.moyu.chuanqirensheng.logic.task.TaskManager
import com.moyu.chuanqirensheng.logic.unlock.UNLOCK_PVP
import com.moyu.chuanqirensheng.logic.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.ui.theme.imageTinyPlus
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.util.PVP_SCREEN
import com.moyu.chuanqirensheng.util.goto

@Composable
fun PvpIcon(modifier: Modifier = Modifier) {
    val unlock = repo.gameCore.getUnlockById(UNLOCK_PVP)
    if (UnlockManager.getUnlockedFlow(unlock)) {
        Column(horizontalAlignment = Alignment.CenterHorizontally, modifier = modifier) {
            EffectButton(modifier = Modifier.graphicsLayer {
                translationY = paddingMediumPlus.toPx()
            }, onClick = {
                goto(PVP_SCREEN)
            }) {
                Image(
                    modifier = Modifier
                        .size(ItemSize.LargePlus.itemSize),
                    painter = painterResource(id = R.drawable.pvp_logo),
                    contentDescription = null
                )
                if (PvpManager.pkNumToday.value < MAX_PVP_NUM + VipManager.getExtraPvpNum() || SellManager.getRedFree(
                        SELL_TYPE_PVP
                    ) || TaskManager.pvpTasks.any {
                        TaskManager.getTaskDoneFlow(it) && !it.opened
                    }
                ) {
                    Image(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .padding(ItemSize.LargePlus.itemSize / 20)
                            .size(imageTinyPlus),
                        painter = painterResource(R.drawable.red_icon),
                        contentDescription = null
                    )
                }
            }
            Spacer(modifier = Modifier.size(paddingSmall))
            Text(
                text = stringResource(R.string.pvp),
                maxLines = 2,
                textAlign = TextAlign.Center,
                style = MaterialTheme.typography.h5
            )
        }
    }
}
