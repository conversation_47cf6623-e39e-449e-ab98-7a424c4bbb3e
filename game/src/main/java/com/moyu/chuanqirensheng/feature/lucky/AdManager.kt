package com.moyu.chuanqirensheng.feature.lucky

import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_AD_MONEY_AWARD1
import com.moyu.chuanqirensheng.datastore.KEY_AD_MONEY_AWARD2
import com.moyu.chuanqirensheng.datastore.KEY_AD_MONEY_AWARD3
import com.moyu.chuanqirensheng.datastore.KEY_AD_MONEY_AWARD4
import com.moyu.chuanqirensheng.datastore.KEY_AD_MONEY_AWARD5
import com.moyu.chuanqirensheng.datastore.KEY_AD_MONEY_AWARD6
import com.moyu.chuanqirensheng.datastore.KEY_AD_MONEY_REFRESH_TIME
import com.moyu.chuanqirensheng.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.datastore.getLongFlowByKey
import com.moyu.chuanqirensheng.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.datastore.setLongValueByKey
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isSameDay
import com.moyu.core.model.Lucky
import com.moyu.core.model.sell.toAward
import com.moyu.core.util.RANDOM
import com.moyu.core.util.nextIntClosure
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

val awardedKey = listOf(
    KEY_AD_MONEY_AWARD1,
    KEY_AD_MONEY_AWARD2,
    KEY_AD_MONEY_AWARD3,
    KEY_AD_MONEY_AWARD4,
    KEY_AD_MONEY_AWARD5,
    KEY_AD_MONEY_AWARD6
)

object AdManager {

    val luckyList = repo.gameCore.getLuckyPool()


    fun refreshAdMoney() {
        if (getLongFlowByKey(KEY_AD_MONEY_REFRESH_TIME) == 0L) {
            setLongValueByKey(KEY_AD_MONEY_REFRESH_TIME, getCurrentTime())
        } else {
            val currentTime = getCurrentTime()
            if (!isSameDay(getLongFlowByKey(KEY_AD_MONEY_REFRESH_TIME), currentTime)) {
                setLongValueByKey(KEY_AD_MONEY_REFRESH_TIME, currentTime)
                AwardManager.adMoney.value = 0
                for (key in awardedKey) {
                    setBooleanValueByKey(key, false)
                }
            }
        }
    }

    fun canGetAdMoney(lucky: Lucky): Boolean {
        return AwardManager.adMoney.value >= lucky.price
    }

    fun alreadyGot(lucky: Lucky): Boolean {
        return getBooleanFlowByKey(awardedKey[lucky.id - 1])
    }

    fun getRandomAdMoney(): Int {
        val weights = repo.gameCore.getLuckyWeights()
        val randomValue = RANDOM.nextIntClosure(0, weights.sum())

        return when {
            randomValue <= weights[0] -> repo.gameCore.getLuckyOutputs()[0]
            randomValue <= weights[0] + weights[1] -> repo.gameCore.getLuckyOutputs()[1]
            else -> repo.gameCore.getLuckyOutputs()[2]
        }
    }

    fun getAdMoney(lucky: Lucky) {
        val award = repo.gameCore.getPoolById(lucky.reward).toAward()
        setBooleanValueByKey(awardedKey[lucky.id - 1], true)
        GameApp.globalScope.launch(Dispatchers.Main) {
            AwardManager.gainAward(award)
            Dialogs.awardDialog.value = award
        }
    }
}