package com.moyu.chuanqirensheng.feature.mode

const val MODE_ENDLESS = 1
const val MODE_STAGE = 2
const val MODE_PVP = 3
const val MODE_TOWER = 4

fun Int.isEndless() = this == MODE_ENDLESS
fun Int.isStage() = this == MODE_STAGE
fun Int.isPvp() = this == MODE_PVP
fun Int.isTower() = this == MODE_TOWER

fun Int.createFromMode() = when (this) {
    MODE_ENDLESS -> EndlessGameMode()
    MODE_STAGE -> StageGameMode()
    MODE_PVP -> PVPGameMode()
    MODE_TOWER -> TowerGameMode()
    else -> throw IllegalArgumentException("Unknown game mode: $this")
}

interface GameMode {
    fun getGameMode(): Int

    fun isStage() = getGameMode() == MODE_STAGE
    fun isEndless() = getGameMode() == MODE_ENDLESS
    fun isPvp() = getGameMode() == MODE_PVP
    fun isTower() = getGameMode() == MODE_TOWER
}