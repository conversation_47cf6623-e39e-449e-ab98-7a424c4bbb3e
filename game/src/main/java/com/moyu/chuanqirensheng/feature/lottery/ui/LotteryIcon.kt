package com.moyu.chuanqirensheng.feature.lottery.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.datastore.KEY_FREE_CHEAP_LOTTERY_DONE
import com.moyu.chuanqirensheng.datastore.KEY_FREE_EXPENSIVE_LOTTERY_DONE
import com.moyu.chuanqirensheng.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.feature.lottery.LotteryManager
import com.moyu.chuanqirensheng.logic.unlock.UNLOCK_LOTTERY
import com.moyu.chuanqirensheng.logic.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.ui.theme.imageTinyPlus
import com.moyu.chuanqirensheng.ui.theme.paddingMediumPlus
import com.moyu.chuanqirensheng.ui.theme.paddingSmall
import com.moyu.chuanqirensheng.util.LOTTERY_SCREEN1
import com.moyu.chuanqirensheng.util.LOTTERY_SCREEN2
import com.moyu.chuanqirensheng.util.goto
import com.moyu.chuanqirensheng.util.isNetTimeValid

@Composable
fun LotteryIcon(modifier: Modifier = Modifier) {
    val unlock = repo.gameCore.getUnlockById(UNLOCK_LOTTERY)
    if (UnlockManager.getUnlockedFlow(unlock) && isNetTimeValid()) {
        LaunchedEffect(Unit) {
            LotteryManager.refresh()
        }
        val isCheap = LotteryManager.showCheap()
        Column(horizontalAlignment = Alignment.CenterHorizontally, modifier = modifier) {
            EffectButton(modifier = Modifier.graphicsLayer {
                translationY = paddingMediumPlus.toPx()
            }, onClick = {
                if (isCheap) {
                    goto(LOTTERY_SCREEN1)
                } else {
                    goto(LOTTERY_SCREEN2)
                }
            }) {
                Image(
                    modifier = Modifier
                        .size(ItemSize.LargePlus.itemSize),
                    painter = painterResource(id = R.drawable.lottery_icon),
                    contentDescription = null
                )
                if ((isCheap && !getBooleanFlowByKey(KEY_FREE_CHEAP_LOTTERY_DONE)) ||
                    (!isCheap && !getBooleanFlowByKey(KEY_FREE_EXPENSIVE_LOTTERY_DONE))
                ) {
                    Image(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .padding(ItemSize.LargePlus.itemSize / 20)
                            .size(imageTinyPlus),
                        painter = painterResource(R.drawable.red_icon),
                        contentDescription = null
                    )
                }
            }
            Spacer(modifier = Modifier.size(paddingSmall))
            Text(
                modifier = Modifier.width(ItemSize.LargePlus.frameSize),
                text = if (isCheap) stringResource(id = R.string.lottery_title1) else stringResource(
                    id = R.string.lottery_title2
                ),
                style = MaterialTheme.typography.h5,
                maxLines = 2,
                textAlign = TextAlign.Center
            )
        }
    }
}
