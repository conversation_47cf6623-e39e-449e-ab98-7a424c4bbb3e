package com.moyu.chuanqirensheng.logic.task

import com.moyu.chuanqirensheng.datastore.KEY_GAME_LOGIN_DAY
import com.moyu.chuanqirensheng.datastore.KEY_GAME_TASK_PROGRESS
import com.moyu.chuanqirensheng.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.datastore.setIntValueIfBiggerByKey
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.detail.FATAL_ENEMY
import com.moyu.chuanqirensheng.logic.event.detail.RACE_BATTLE
import com.moyu.chuanqirensheng.logic.talent.TalentManager
import com.moyu.core.GameCore
import com.moyu.core.model.ally.Ally
import com.moyu.core.model.event.Event
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.role.Role
import com.moyu.core.model.sell.Award
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.isAdventure
import com.moyu.core.model.skill.isBattleSkill

fun getLoginDays(): Int {
    return getIntFlowByKey(KEY_GAME_LOGIN_DAY)
}

//  1=次数，2=天数
fun onTaskStartGameTime() {
    setIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.START_GAME.id + "_1", 1)
}

fun onTaskStartGameDay() {
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.START_GAME.id + "_2")
    increaseIntValueByKey(KEY_GAME_LOGIN_DAY)
}

// 1=x年，100=维持100年x次
// 200=维持200年x次
// todo 避免重复increase
var lastCountryName100 = ""
var lastCountryName200 = ""
var lastCountryName300 = ""
var lastCountryName500 = ""
fun onTaskAge(age: Int) {
    setIntValueIfBiggerByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.AGE.id + "_1", age)
    setIntValueIfBiggerByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.AGE.id + "_1", age)
    if (age >= 100) {
        if (lastCountryName100 != BattleManager.countryName.value) {
            lastCountryName100 = BattleManager.countryName.value
            increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.AGE.id + "_100")
            increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.AGE.id + "_100")
        }
    }
    if (age >= 200) {
        if (lastCountryName200 != BattleManager.countryName.value) {
            lastCountryName200 = BattleManager.countryName.value
            increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.AGE.id + "_200")
            increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.AGE.id + "_200")
        }
    }
    if (age >= 300) {
        if (lastCountryName300 != BattleManager.countryName.value) {
            lastCountryName300 = BattleManager.countryName.value
            increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.AGE.id + "_300")
            increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.AGE.id + "_300")
        }
    }
    if (age >= 500) {
        if (lastCountryName500 != BattleManager.countryName.value) {
            lastCountryName500 = BattleManager.countryName.value
            increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.AGE.id + "_500")
            increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.AGE.id + "_500")
        }
    }
}

fun onTaskEnterEvent(event: Event) {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.ENTER_EVENT.id + "_${event.play}")
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.ENTER_EVENT.id + "_${event.play}")
}

fun onTaskKillEnemy(enemies: List<Role>) {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.KILL.id, enemies.size)
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.KILL.id,
        enemies.size
    )
    enemies.forEach {
        increaseIntValueByKey(
            KEY_GAME_TASK_PROGRESS + TaskEvent.KILL.id + "_${it.getRace().raceType}", 1
        )
        increaseIntValueByKey(
            FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.KILL.id + "_${it.getRace().raceType}", 1
        )
    }
}

fun onTaskDoneEvent(event: Event) {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.DONE_EVENT.id + "_${event.play}")
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.DONE_EVENT.id + "_${event.play}")
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.DONE_EVENT.id + "_${event.id}")
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.DONE_EVENT.id + "_${event.id}")
}

fun onTaskAdvProp(adventureProps: AdventureProps) {
    repeat(7) {
        setIntValueIfBiggerByKey(
            KEY_GAME_TASK_PROGRESS + TaskEvent.ADV_PROP.id + "_${it + 1}",
            adventureProps.getPropertyByTarget(it + 1)
        )
        setIntValueIfBiggerByKey(
            FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.ADV_PROP.id + "_${it + 1}",
            adventureProps.getPropertyByTarget(it + 1)
        )
    }
}

// 记录所有物品id的话，会导致存档过大，这里需要过滤下
val needRecordItemIdList = GameCore.instance.getGameTaskPool().flatMap {
    it.subType
}.filter { it > 10000 }

fun onTaskGetItem(award: Award) {
    if (award.allies.isNotEmpty()) {
        // 这里记录获得军团卡总数
        increaseIntValueByKey(
            KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_1",
            award.allies.size
        )
        increaseIntValueByKey(
            FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_1",
            award.allies.size
        )
        // 这里记录获得军团卡类型
        award.allies.forEach {
            increaseIntValueByKey(
                KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_1" + "_${it.type}",
                award.allies.size
            )
            increaseIntValueByKey(
                FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_1" + "_${it.type}",
                award.allies.size
            )
        }
        // 这里记录获得军团卡id，过滤下，防止存档过大
        award.allies.filter { it.id in needRecordItemIdList }.forEach {
            increaseIntValueByKey(
                KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_1" + "_${it.id}",
                award.allies.size
            )
            increaseIntValueByKey(
                FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_1" + "_${it.id}",
                award.allies.size
            )
        }
    }
    val advSkills = award.skills.filter { it.isAdventure() }
    if (advSkills.isNotEmpty()) {
        // 这里记录获得冒险技能卡总数
        increaseIntValueByKey(
            KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_2",
            advSkills.size
        )
        increaseIntValueByKey(
            FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_2",
            advSkills.size
        )
        // 这里记录获得冒险技能卡类型
        advSkills.forEach {
            increaseIntValueByKey(
                KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_2" + "_${it.elementType}",
                advSkills.size
            )
            increaseIntValueByKey(
                FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_2" + "_${it.elementType}",
                advSkills.size
            )
        }
        // 这里记录获得冒险技能卡id，过滤下，防止存档过大
        advSkills.filter { it.id in needRecordItemIdList }.forEach {
            increaseIntValueByKey(
                KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_${it.id}",
                advSkills.size
            )
            increaseIntValueByKey(
                FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_${it.id}",
                advSkills.size
            )
        }
    }
    val battleSkills = award.skills.filter { it.isBattleSkill() }
    if (battleSkills.isNotEmpty()) {
        // 这里记录获得战斗技能卡总数
        increaseIntValueByKey(
            KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_3",
            battleSkills.size
        )
        increaseIntValueByKey(
            FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_3",
            battleSkills.size
        )
        // 这里记录获得战斗技能卡类型
        battleSkills.forEach {
            increaseIntValueByKey(
                KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_3" + "_${it.elementType}",
                battleSkills.size
            )
            increaseIntValueByKey(
                FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_3" + "_${it.elementType}",
                battleSkills.size
            )
        }
        // 这里记录获得战斗技能卡id，过滤下，防止存档过大
        battleSkills.filter { it.id in needRecordItemIdList }.forEach {
            increaseIntValueByKey(
                KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_3" + "_${it.id}",
                battleSkills.size
            )
            increaseIntValueByKey(
                FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_3" + "_${it.id}",
                battleSkills.size
            )
        }
    }
    val heroes = award.heroes
    if (heroes.isNotEmpty()) {
        // 这里记录获得史诗人物总数
        increaseIntValueByKey(
            KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_4",
            heroes.size
        )
        increaseIntValueByKey(
            FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_4",
            heroes.size
        )
        // 这里记录获得史诗人物类型
        heroes.forEach {
            increaseIntValueByKey(
                KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_4" + "_${it.elementType}",
                heroes.size
            )
            increaseIntValueByKey(
                FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_4" + "_${it.elementType}",
                heroes.size
            )
        }
        // 这里记录获得史诗人物id，过滤下，防止存档过大
        heroes.forEach {
            increaseIntValueByKey(
                KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_4" + "_${it.id}",
                heroes.size
            )
            increaseIntValueByKey(
                FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_4" + "_${it.id}",
                heroes.size
            )
        }
    }
    if (award.elements.any { it != 0 }) {
        award.elements.forEachIndexed { index, i ->
            if (i > 0) {
                increaseIntValueByKey(
                    KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_5" + "_${index + 1}",
                    i
                )
                increaseIntValueByKey(
                    FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_5" + "_${index + 1}",
                    i
                )
            }
        }
    }
    if (award.badges.any { it != 0 }) {
        award.badges.forEachIndexed { index, i ->
            if (i > 0) {
                increaseIntValueByKey(
                    KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_8" + "_${index + 1}",
                    i
                )
                increaseIntValueByKey(
                    FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_8" + "_${index + 1}",
                    i
                )
            }
        }
    }
    repeat(award.tcgs.size) {
        increaseIntValueByKey(
            KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_26"
        )
        increaseIntValueByKey(
            FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.GAIN_ITEM.id + "_26"
        )
    }
}

fun onLoseAlly(ally: Ally) {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.LOSE_ITEM.id + "_1", 1)
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.LOSE_ITEM.id + "_1", 1)

    increaseIntValueByKey(
        KEY_GAME_TASK_PROGRESS + TaskEvent.LOSE_ITEM.id + "_1" + "_${ally.type}",
        1
    )
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.LOSE_ITEM.id + "_1" + "_${ally.type}",
        1
    )
}

fun onTaskLoseHero(target: Skill) {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.LOSE_ITEM.id + "_4", 1)
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.LOSE_ITEM.id + "_4", 1)

    increaseIntValueByKey(
        KEY_GAME_TASK_PROGRESS + TaskEvent.LOSE_ITEM.id + "_4" + "_${target.elementType}",
        1
    )
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.LOSE_ITEM.id + "_4" + "_${target.elementType}",
        1
    )
}

fun onTaskReputationLevel(reputationLevels: List<Int>) {
    reputationLevels.forEachIndexed { index, i ->
        setIntValueIfBiggerByKey(
            KEY_GAME_TASK_PROGRESS + TaskEvent.REPUTATION_LEVEL.id + "_${index + 1}",
            i
        )
        setIntValueIfBiggerByKey(
            FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.REPUTATION_LEVEL.id + "_${index + 1}",
            i
        )
    }
}

/**
 * 抵御入侵是 1，2
 * 战役是3，4
 * 攻城战是5，6
 */
fun onTaskFatalPlay(type: Int, event: Event) {
    val realType = if (event.play == FATAL_ENEMY) type else if (event.play == RACE_BATTLE) type + 4 else type + 2
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.FATAL_PLAY.id + "_${realType}", 1)
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.FATAL_PLAY.id + "_${realType}",
        1
    )
}

fun onTaskCountryLevel(level: Int) {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.COUNTRY_LEVEL.id + "_$level", 1)
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.COUNTRY_LEVEL.id + "_$level",
        1
    )
}

fun onTaskBuyFree() {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.BUY.id + "_1", 1)
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.BUY.id + "_1", 1)
}

fun onTaskBuyAlly() {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.BUY.id + "_2", 1)
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.BUY.id + "_2", 1)
}

fun onTaskBuySkill() {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.BUY.id + "_3", 1)
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.BUY.id + "_3", 1)
}

fun onTaskBuyHero() {
    increaseIntValueByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.BUY.id + "_4", 1)
    increaseIntValueByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.BUY.id + "_4", 1)
}

fun onTaskTalentUp(level: Int) {
    // 1=任意升到x级，2=总等级升到x级
    setIntValueIfBiggerByKey(
        KEY_GAME_TASK_PROGRESS + TaskEvent.TALENT_UP.id + "_1",
        level
    )
    setIntValueIfBiggerByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.TALENT_UP.id + "_1",
        level
    )
    val totalLevel = TalentManager.talents.values.sum()
    setIntValueIfBiggerByKey(KEY_GAME_TASK_PROGRESS + TaskEvent.TALENT_UP.id + "_2", totalLevel)
    setIntValueIfBiggerByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.TALENT_UP.id + "_2",
        totalLevel
    )
}



fun onDrawAllyCard(count: Int) {
    // 1=任意升到x级，2=总等级升到x级
    increaseIntValueByKey(
        KEY_GAME_TASK_PROGRESS + TaskEvent.DRAW.id + "_1",
        count
    )
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.DRAW.id + "_1",
        count
    )
    increaseIntValueByKey(
        KEY_GAME_TASK_PROGRESS + TaskEvent.DRAW.id,
        count
    )
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.DRAW.id,
        count
    )
}

fun onDrawSkillCard(count: Int) {
    // 1=任意升到x级，2=总等级升到x级
    increaseIntValueByKey(
        KEY_GAME_TASK_PROGRESS + TaskEvent.DRAW.id + "_2",
        count
    )
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.DRAW.id + "_2",
        count
    )
    increaseIntValueByKey(
        KEY_GAME_TASK_PROGRESS + TaskEvent.DRAW.id,
        count
    )
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.DRAW.id,
        count
    )
}

fun onDrawHeroCard(count: Int) {
    // 1=任意升到x级，2=总等级升到x级
    increaseIntValueByKey(
        KEY_GAME_TASK_PROGRESS + TaskEvent.DRAW.id + "_3",
        count
    )
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.DRAW.id + "_3",
        count
    )
    increaseIntValueByKey(
        KEY_GAME_TASK_PROGRESS + TaskEvent.DRAW.id,
        count
    )
    increaseIntValueByKey(
        FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.DRAW.id,
        count
    )
}
