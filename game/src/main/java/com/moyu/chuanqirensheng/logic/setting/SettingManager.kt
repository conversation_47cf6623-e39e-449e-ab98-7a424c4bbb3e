package com.moyu.chuanqirensheng.logic.setting

import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.datastore.KEY_MUTE_DIALOG
import com.moyu.chuanqirensheng.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.remoteab.RemoteAbConfigManager

object SettingManager {
    val noDialog = mutableStateOf(false)
    fun init() {
        noDialog.value = getBooleanFlowByKey(KEY_MUTE_DIALOG, RemoteAbConfigManager.muteDialog())
    }

    fun switchMuteDialog() {
        noDialog.value = !noDialog.value
        setBooleanValueByKey(KEY_MUTE_DIALOG, noDialog.value)
    }
}