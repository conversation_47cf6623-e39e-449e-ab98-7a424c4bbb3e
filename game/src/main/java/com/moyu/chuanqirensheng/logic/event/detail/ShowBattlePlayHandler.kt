package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.runtime.Composable
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.chuanqirensheng.logic.event.createEnemyRole
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.event.BattleLayout
import com.moyu.core.model.event.Event

const val SHOW_BATTLE_PLAY = 26
class ShowBattlePlayHandler(
    override val skipWin: Boolean = false,
    override val dialogJump: Boolean = false,
    override val playId: Int = SHOW_BATTLE_PLAY
): PlayHandler() {

    @Composable
    override fun Layout(event: Event) {
        BattleLayout(event = event)
    }

    override fun onEventSelect(event: Event) {
        // 决斗，普通战斗,宿敌战斗,诅咒战斗
        val pool = repo.gameCore.getPoolById(event.playPara1.first())
        val enemies = pool.pool.map {
            repo.gameCore.getRaceById(it)
        }.map {
            createEnemyRole(it, event)
        }.toMutableList()
        repo.setCurrentEnemies(enemies)
    }
}