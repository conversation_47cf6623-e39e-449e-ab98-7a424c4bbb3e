package com.moyu.chuanqirensheng.logic.event

import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.BuildConfig
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.api.postPvpRankData
import com.moyu.chuanqirensheng.api.postRankDataApi
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.cloud.MAX_AGE
import com.moyu.chuanqirensheng.datastore.KEY_ENDING_NUM
import com.moyu.chuanqirensheng.datastore.KEY_GAME_TASK_PROGRESS
import com.moyu.chuanqirensheng.datastore.KEY_GUIDE_INDEX
import com.moyu.chuanqirensheng.datastore.KEY_INVISIBLE_IN_RANK
import com.moyu.chuanqirensheng.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.datastore.getIntFlowByKey
import com.moyu.chuanqirensheng.datastore.increaseIntValueByKey
import com.moyu.chuanqirensheng.datastore.setIntValueByKey
import com.moyu.chuanqirensheng.debug.DebugManager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.award.toAward
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager.battleRolePositions
import com.moyu.chuanqirensheng.logic.event.detail.BuildPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.CrisisPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.CurseEnemyBattlePlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.DevelopPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.DiplomacyPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.DisasterPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.FatalEnemyBattlePlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.HealPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.HeroPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.MercenaryPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.NegotiationPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.NonePlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.NormalBattlePlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.PolicyPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.RaceBattlePlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.RiotPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.SHOW_BATTLE_PLAY
import com.moyu.chuanqirensheng.logic.event.detail.SchoolPlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.ShowBattlePlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.SingleBattlePlayHandler
import com.moyu.chuanqirensheng.logic.event.detail.WorldPlayHandler
import com.moyu.chuanqirensheng.logic.guide.GuideManager
import com.moyu.chuanqirensheng.logic.record.SaveManager
import com.moyu.chuanqirensheng.logic.skill.AgeEvent
import com.moyu.chuanqirensheng.logic.skill.EnterEvent
import com.moyu.chuanqirensheng.logic.skill.FailedEvent
import com.moyu.chuanqirensheng.logic.skill.SucceededEvent
import com.moyu.chuanqirensheng.logic.story.Ending
import com.moyu.chuanqirensheng.logic.story.StoryManager
import com.moyu.chuanqirensheng.logic.talent.TalentManager
import com.moyu.chuanqirensheng.logic.task.FOREVER
import com.moyu.chuanqirensheng.logic.task.TaskEvent
import com.moyu.chuanqirensheng.logic.task.onTaskAge
import com.moyu.chuanqirensheng.logic.task.onTaskDoneEvent
import com.moyu.chuanqirensheng.logic.task.onTaskEnterEvent
import com.moyu.chuanqirensheng.logic.tcg.TcgManager
import com.moyu.chuanqirensheng.model.record.AllRankData
import com.moyu.chuanqirensheng.music.MusicManager
import com.moyu.chuanqirensheng.music.playerMusicByScreen
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.sub.report.ReportManager
import com.moyu.chuanqirensheng.sub.review.GameReviewManager
import com.moyu.chuanqirensheng.util.EVENT_DETAIL_SCREEN
import com.moyu.chuanqirensheng.util.EVENT_SELECT_SCREEN
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.getVersionCode
import com.moyu.chuanqirensheng.util.goto
import com.moyu.core.GameCore
import com.moyu.core.model.event.Event
import com.moyu.core.model.info.BattleInfoType
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.role.Role
import com.moyu.core.model.timing.Timing
import com.moyu.core.music.SoundEffect
import com.moyu.core.util.RANDOM
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import timber.log.Timber

object EventManager {
    val selectedEvent = mutableStateOf<Event?>(null)// 当前选中事件
    val selectionEvents = mutableStateListOf<Event>() // 当前可选事件
    private val startEventHelper = StartEventHelper()
    val eventRecorder = EventRecorder()
    val worldEventTime = mutableStateListOf<Int>()
    val currentBgMusic = mutableStateOf(0)

    private val eventHandlerClasses = hashMapOf(
        1 to { DevelopPlayHandler() },
        2 to { PolicyPlayHandler() },
        3 to { BuildPlayHandler() },
        4 to { HealPlayHandler() },
        5 to { MercenaryPlayHandler() },
        6 to { SchoolPlayHandler() },
        7 to { HeroPlayHandler() },
        8 to { DiplomacyPlayHandler() },
        9 to { DisasterPlayHandler() },
        10 to { CrisisPlayHandler() },
        11 to { NegotiationPlayHandler() },
        12 to { RiotPlayHandler() },
        31 to { WorldPlayHandler() },
        21 to { FatalEnemyBattlePlayHandler() },
        22 to { NormalBattlePlayHandler() },
        23 to { RaceBattlePlayHandler() },
        24 to { CurseEnemyBattlePlayHandler() },
        25 to { SingleBattlePlayHandler() },
        26 to { ShowBattlePlayHandler() },
    )
    private val playHandlers = hashMapOf<Event, PlayHandler>()

    fun init() {
        // null
    }

    fun onNewGame() {
        currentBgMusic.value = MusicManager.getRandomDungeonMusic()
        playHandlers.clear()
        startEventHelper.clear()
        startEventHelper.init()
        eventRecorder.clear()
        worldEventTime.clear()
        repeat(10) {
            // 随机世界事件的时间
            val randomNumber = if (DebugManager.worldEvent) {
                it * 30 + 21
            } else {
                ((it * 30 + 2)..(it + 1) * 30).random()
            }
            worldEventTime.add(randomNumber)
        }
    }

    fun getEventSelectTitle(): String {
        return startEventHelper.getEventTitle()
            ?: let {
                if (selectionEvents.firstOrNull()?.isWorldEvent() == true) {
                    GameApp.instance.getWrapString(R.string.world_event_cast)
                } else {
                    GameApp.instance.getWrapString(R.string.the) + BattleManager.adventureProps.value.age.ageToYear() + GameApp.instance.getWrapString(
                        R.string.choose_event
                    )
                }
            }
    }

    fun getNextEvents(): List<Event> {
        if (selectionEvents.isNotEmpty()) { // 保护
            return selectionEvents
        }

        return startEventHelper.getNextEvents() ?: let {
            if (DebugManager.allEvent) {
                return repo.gameCore.getEventPool().filterNot { it.isStartingEvent() }
                    .map { it.createUUID() }
            }

            val age = BattleManager.adventureProps.value.age
            val fixedAppear =
                repo.gameCore.getEventPool().filterNot { it.isStartingEvent() || it.isWorldEvent() }
                    .filter { StoryManager.eventInStoryBag(it) }
                    .any { it.appear == age % 10 }
            val worldEvents = repo.gameCore.getEventPool().filter { it.isWorldEvent() }
                .filter { StoryManager.eventInStoryBag(it) }
                .filter { age >= it.age[0] && age <= it.age[1] }
            val allEvents = if (age in worldEventTime) {
                // 随机的世界事件命中
                worldEvents.shuffled(RANDOM).take(1)
            } else if (fixedAppear) {
                /**
                 * 如果这一年，存在appear和这一年相等的event，
                 * 筛选出来，直接随机，比如第1年，因为存在appear==1的event，全部筛选出来，随机3个，不需要看same
                 */
                repo.gameCore.getEventPool().asSequence()
                    .filterNot { it.isStartingEvent() || it.isWorldEvent() }
                    .filter { age >= it.age[0] && age <= it.age[1] }
                    .filter { it.appear == age % 10 }
                    .filter { StoryManager.eventInStoryBag(it) }
                    .map { it.createUUID() }
                    .filter(eventRecorder.filterNotUsed)
                    .filter(eventRecorder.filterFront)
                    .filter(eventRecorder.filterDisappear)
                    .toList()
            } else {
                /**
                 * 如果这一年，不存在appear和这一年相等的event，
                 * 则从所有appear == -1的events里随机选一个，
                 * 再根据随机到的这个event的same和play，选3个
                 * 注意same==1则要求play相同
                 * same==0则不要求
                 */
                val innerList = repo.gameCore.getEventPool().asSequence()
                    .filterNot { it.isStartingEvent() || it.isWorldEvent() }
                    .filter { age >= it.age[0] && age <= it.age[1] }
                    .filter { it.appear == -1 }
                    .filter { StoryManager.eventInStoryBag(it) }
                    .map { it.createUUID() }
                    .filter(eventRecorder.filterNotUsed)
                    .filter(eventRecorder.filterFront)
                    .filter(eventRecorder.filterDisappear)
                    .toList().shuffled(RANDOM)
                val randomOne = innerList.first()
                val randomPlay = randomOne.play
                val randomSame = randomOne.same

                if (randomSame == 1) {
                    innerList.filter { it.same == randomSame && it.play == randomPlay }
                } else {
                    innerList.filter { it.same == randomSame }
                }
            }

            val events = allEvents.shuffled(RANDOM).take(EVENT_SIZE)
            selectionEvents.clear()
            selectionEvents.addAll(events)
            return events.map { it.createUUID() }
        }
    }

    fun selectEvent(event: Event): Boolean {
        return if (triggerEvent(event)) {
            // 成功，根据玩法进行游戏
            if (!startEventHelper.doSelectEvent(event)) {
                if (!event.isStartingEvent()) {
                    // 前3个事件，不需要切到事件已选定状态
                    goto(EVENT_DETAIL_SCREEN)
                }
                selectedEvent.value = event
                SaveManager.selectEvent(event)
                selectionEvents.clear()

                doEvent(event)
                playerMusicByScreen()
                if (event.play in specialEventPlayIds) {
                    GameCore.instance.onBattleEffect(SoundEffect.SelectEventSpecial)
                } else {
                    GameCore.instance.onBattleEffect(SoundEffect.SelectEvent)
                }
            }
            true
        } else {
            // 失败
            GameApp.instance.getWrapString(R.string.event_not_ready).toast()
            false
        }
    }

    fun getOrCreateHandler(event: Event): PlayHandler {
        val handler = playHandlers[event]
        return if (handler == null) {
            val newHandler = eventHandlerClasses[event.play]?.invoke() ?: NonePlayHandler()
            playHandlers[event] = newHandler
            newHandler
        } else {
            handler
        }
    }

    private fun doEvent(event: Event) {
        GameApp.globalScope.launch(Dispatchers.Main) {
            onTaskEnterEvent(event)

            repo.onBattleInfo(
                "\n" + (BattleManager.adventureProps.value.age.toString() + GameApp.instance.getWrapString(
                    R.string.choose_event1
                ) + "【" + event.name + "】" + GameApp.instance.getWrapString(
                    com.moyu.core.R.string.event
                )),
                BattleInfoType.ExtraSkill
            )
            adventureSkillTrigger(triggerSkill = EnterEvent.copy(mainId = event.play))
            BattleManager.onEventSelect()
            getOrCreateHandler(event).eventSelect(event)
        }
    }

    fun doEventBattleResult(event: Event?, result: Boolean) {
        if (event == null) return
        if (getOrCreateHandler(event).eventFinished.value) return

        BattleManager.onBattleEnd()
        playerMusicByScreen() // 音乐
        GameApp.globalScope.launch(Dispatchers.Main) {
            if (!result && event.play != SHOW_BATTLE_PLAY) {
                // 需要杀死你的出战军团卡，因为可能你强行退出战斗了
                repo.battle.value.getAllPlayers().filter { !it.isDeath() }.forEach {
                    BattleManager.updateAllyInGameById(it, 0)
                }
            }
            getOrCreateHandler(event).apply {
                eventFinished.value = true
                eventResult.value = result
                doEventResult(event, result)
            }
        }
    }

    fun doEventResult(event: Event, result: Boolean) {
        val adjustedResult =
            if (DebugManager.eventWin || event.forceWin()) {
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.force_win),
                    BattleInfoType.ExtraSkill
                )
                true
            } else if (DebugManager.eventLose || event.forceLose()) {
                repo.onBattleInfo(
                    GameApp.instance.getWrapString(R.string.force_lose),
                    BattleInfoType.ExtraSkill
                )
                false
            } else result

        if (eventRecorder.addResult(event, adjustedResult)) {
            // 这里说明不可重复事件重复进行了结算，直接跳回事件选择页面，极端情况，防止卡死
            gotoNextEvent(event, false)
            return
        }
        GameApp.globalScope.launch(Dispatchers.Main) {
            repo.onBattleInfo(
                "【${event.name}】${GameApp.instance.getWrapString(R.string.events)}${
                    if (result) GameApp.instance.getWrapString(R.string.win) else GameApp.instance.getWrapString(
                        R.string.lose
                    )
                }", BattleInfoType.ExtraSkill
            )
            if (adjustedResult) {
                if (event.isStartingEvent()) {
                    // 初始3个事件完成直接跳下一个
                    val award = event.toAward(win = true).copy(showQuestion = false)
                    AwardManager.gainAward(award)
                    gotoNextEvent(event, true)
                } else {
                    // 其他事件在弹窗里跳转
                    Dialogs.eventPassDialog.value = event
                }
            } else {
                Dialogs.eventFailDialog.value = event
            }
        }
    }

    fun getUsedEvents(): List<Event> {
        return eventRecorder.usedEvents
    }

    fun getSucceededEvents(): List<Event> {
        return eventRecorder.succeededEvents
    }

    fun getFailedEvents(): List<Event> {
        return eventRecorder.failedEvents
    }

    fun gotoNextEvent(event: Event?, pass: Boolean, agePlus: Int = 1) {
        GameApp.globalScope.launch(Dispatchers.Main) {
            // 防止数据过大
            battleRolePositions.clear()
            selectionEvents.clear()
            if (pass) {
                adventureSkillTrigger(triggerSkill = SucceededEvent.copy(mainId = event?.play ?: 0))
            } else {
                adventureSkillTrigger(triggerSkill = FailedEvent.copy(mainId = event?.play ?: 0))
            }
            val story = StoryManager.saveStory(
                repo.you.value,
                eventRecorder.usedEvents,
                eventRecorder.succeededEvents
            )
            if (pass && event != null) {
                onTaskDoneEvent(event)
            }
            if (BattleManager.allyGameData.isEmpty() || BattleManager.allyGameData.all { it.isDead() }) {
                ReportManager.allAllyDie()
            }

            if (BattleManager.adventureProps.value.population <= 0
                || BattleManager.adventureProps.value.age >= MAX_AGE
            ) {
                ending(story)
            } else {
                if (event?.isWorldEvent() != true) {
                    adventureSkillTrigger(triggerSkill = AgeEvent)
                }
                if (startEventHelper.isStartEnded()) {
                    // 初始阶段年龄不增加
                    if (BattleManager.adventureProps.value.age !in worldEventTime) {
                        BattleManager.gainAdventureProp(AdventureProps(age = agePlus))
                        repeat(agePlus) {
                            BattleManager.oneYearPass()
                        }
                    } else {
                        worldEventTime.remove(BattleManager.adventureProps.value.age)
                    }
                    onTaskAge(BattleManager.adventureProps.value.age)
                    if (GuideManager.guideIndex.value == 6) {
                        GuideManager.guideIndex.value += 1
                        GuideManager.showGuide.value = true
                        setIntValueByKey(KEY_GUIDE_INDEX, GuideManager.guideIndex.value)
                    }
                    if (GuideManager.guideIndex.value == 8 && BattleManager.adventureProps.value.age == 2) {
                        GuideManager.guideIndex.value += 1
                        GuideManager.showGuide.value = true
                        setIntValueByKey(KEY_GUIDE_INDEX, GuideManager.guideIndex.value)
                    }
                    repo.you.value.markSkillNewTurn(repo.you.value)
                    repo.you.value.clearGrave(Timing.TurnBegin)
                }

                if (BattleManager.adventureProps.value.age == 300) {
                    GameApp.instance.getWrapString(R.string.tips_300).toast()
                }
                repo.updateYou()
                goto(EVENT_SELECT_SCREEN)
                playerMusicByScreen()
                GameReviewManager.checkTriggerReviewDialog(BattleManager.adventureProps.value.age)
            }
        }
    }

    fun ending(ending: Ending?) {
        ending?.let {
            Dialogs.endingDialog.value = ending
            repo.inGame.value = false
            playerMusicByScreen() // 音乐
            if (it.ending != GameApp.instance.getWrapString(R.string.none)) { // todo 翻译要注意
                increaseIntValueByKey(KEY_ENDING_NUM)
            }
        }
        // todo 记得打开这个开关
        if ((ending?.age?:0) >= 200) {
            uploadRanks()
        }
    }

    fun uploadRanks() {
        GameApp.globalScope.launch(Dispatchers.IO) {
            try {
                postRankDataApi(
                    AllRankData(
                        time = getCurrentTime(),
                        versionCode = getVersionCode(),
                        userName = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) "????" else GameApp.instance.getUserName()
                            ?: GameApp.instance.getWrapString(R.string.not_login),
                        userId = GameApp.instance.getObjectId() ?: "0",
                        userPic = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) "deafult_head" else GameApp.instance.getAvatarUrl() ?: "0",
                        tcgValue = TcgManager.tcgCards.size,
                        loseAlly = 0,
                        age = Integer.min(
                            MAX_AGE, getIntFlowByKey(
                                FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.AGE.id + "_1",
                                1
                            )
                        ),
                        killEnemy = getIntFlowByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.KILL.id),
                        talentNum = TalentManager.talents.values.sum(),
                        endingNum = getIntFlowByKey(KEY_ENDING_NUM),
                        keyNum = AwardManager.key.value,
                        diamondNum = AwardManager.diamond.value,
                        electric = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) AwardManager.electric.value else 0,
                        platformChannel = GameApp.instance.resources.getString(R.string.platform_channel),
                    )
                )
            } catch (e: Exception) {
                Timber.e(e)
            }
        }
    }

    fun uploadPvpRank(allies: List<Role>) {
        if ((!BuildConfig.FLAVOR.contains("Lite") || DebugManager.unlockAll)) {
            GameApp.globalScope.launch(Dispatchers.IO) {
                try {
                    postPvpRankData(
                        AllRankData(
                            time = getCurrentTime(),
                            versionCode = getVersionCode(),
                            userName = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) "????" else GameApp.instance.getUserName()
                                ?: GameApp.instance.getWrapString(R.string.not_login),
                            userId = GameApp.instance.getObjectId() ?: "0",
                            userPic = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) "deafult_head" else GameApp.instance.getAvatarUrl() ?: "0",
                            tcgValue = TcgManager.tcgCards.size,
                            loseAlly = 0,
                            age = Integer.min(
                                MAX_AGE, getIntFlowByKey(
                                    FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.AGE.id + "_1",
                                    1
                                )
                            ),
                            killEnemy = getIntFlowByKey(FOREVER + KEY_GAME_TASK_PROGRESS + TaskEvent.KILL.id),
                            talentNum = TalentManager.talents.values.sum(),
                            endingNum = getIntFlowByKey(KEY_ENDING_NUM),
                            keyNum = AwardManager.key.value,
                            diamondNum = AwardManager.diamond.value,
                            electric = if (getBooleanFlowByKey(KEY_INVISIBLE_IN_RANK)) AwardManager.electric.value else 0,
                            platformChannel = GameApp.instance.resources.getString(R.string.platform_channel),
                            pvpScore = PvpManager.pvpScore.value,
                            pvpData = PvpManager.getPvpData(allies),
                        )
                    )
                } catch (e: Exception) {
                    Timber.e(e)
                }
            }
        }
    }

    fun extraIsGameOver(): Boolean {
        if (repo.gameMode.value.isTower() && TowerManager.targetLevel.value.type.first() == 4) {
            return repo.battleTurn.value > (TowerManager.targetLevel.value.playPara1.first())
        }
        return false
    }

    fun resetEventRecorder(used: List<Event>, success: List<Event>, failed: List<Event>) {
        eventRecorder.resetEvents(used, success, failed)
    }
}