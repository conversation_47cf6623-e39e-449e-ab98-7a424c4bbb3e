package com.moyu.chuanqirensheng.logic.event

import androidx.compose.runtime.mutableStateListOf
import com.moyu.core.model.event.Event

class EventRecorder {

    val succeededEvents = mutableStateListOf<Event>()
    val failedEvents = mutableStateListOf<Event>()
    val usedEvents = mutableStateListOf<Event>()
    
    fun clear() {
        succeededEvents.clear()
        failedEvents.clear()
        usedEvents.clear()
    }

    fun addResult(event: Event, adjustedResult: Boolean): Boolean {
        if (usedEvents.contains(event)) return true
        usedEvents.add(event)
        if (adjustedResult) {
            succeededEvents.add(event)
        } else {
            failedEvents.add(event)
        }
        return false
    }

    fun resetEvents(used: List<Event>, success: List<Event>, failed: List<Event>) {
        usedEvents.clear()
        usedEvents.addAll(used)
        succeededEvents.clear()
        succeededEvents.addAll(success)
        failedEvents.clear()
        failedEvents.addAll(failed)
    }

    val filterNotUsed: (Event) -> Boolean = {
        it !in usedEvents
    }
    val filterFront: (Event) -> Boolean = {
        it.front.first() == 0 || it.front.intersect(usedEvents.map { it.id }
            .toSet()).isNotEmpty()
    }
    val filterDisappear: (Event) -> Boolean = {
        it.disappear.first() == 0 || it.disappear.intersect(usedEvents.map { it.id }
            .toSet())
            .isEmpty()
    }
}