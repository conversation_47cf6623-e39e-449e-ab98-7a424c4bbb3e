package com.moyu.chuanqirensheng.logic.hero

import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp


fun Int.getQualityName(): String {
    return when (this) {
        1 -> GameApp.instance.getWrapString(R.string.blue_quality)
        2 -> GameApp.instance.getWrapString(R.string.purple_quality)
        else -> GameApp.instance.getWrapString(R.string.orange_quality)
    }
}

fun Int.getQualityRes(): Int {
    return when (this) {
        1 -> R.drawable.item_quality_1
        2 -> R.drawable.item_quality_2
        else -> R.drawable.item_quality_3
    }
}

fun Int.getQualityFrame(): Int {
    return when (this) {
        1 -> R.drawable.item_quality_1
        2 -> R.drawable.item_quality_2
        3 -> R.drawable.item_quality_3
        else -> R.drawable.item_quality_0
    }
}

fun Int.getCardQualityFrame(): Int {
    return when (this) {
        1 -> R.drawable.card_frame_green
        2 -> R.drawable.card_frame_blue
        else -> R.drawable.card_frame_orange
    }
}