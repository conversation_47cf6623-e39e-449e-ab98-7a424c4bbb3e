package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.chuanqirensheng.logic.hero.getQualityName
import com.moyu.chuanqirensheng.repository.Dialogs
import com.moyu.chuanqirensheng.repository.toast
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.equip.SingleHeroView
import com.moyu.chuanqirensheng.screen.skill.EmptyIconView
import com.moyu.chuanqirensheng.ui.theme.paddingMedium
import com.moyu.core.model.event.Event
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.skill.Skill
import com.moyu.core.model.skill.quality
import com.moyu.core.util.chance
import com.moyu.core.util.realValueToDotWithOneDigits
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.lang.Double.max

class NegotiationPlayHandler(
    override val skipWin: Boolean = false,
    override val dialogJump: Boolean = false,
    override val playId: Int = 11
) : PlayHandler() {
    private val ally1 = mutableStateOf<Skill?>(null)
    private val qualityTarget = mutableStateOf(0)
    private val showSelect1 = {
        Dialogs.selectOneHeroDialog.value = Pair({
            it != ally1.value
        }, {
            if (qualityTarget.value > it.quality()) {
                (GameApp.instance.getWrapString(R.string.need) + qualityTarget.value.getQualityName() + GameApp.instance.getWrapString(
                    R.string.level
                )).toast()
            } else {
                ally1.value = it
            }
        })
    }

    @Composable
    override fun Layout(event: Event) {
        val decrease = event.playPara2[1].toFloat() * 100
        val quality = event.playPara1[1].getQualityName()
        val propertyEnum = event.playPara1.first()
        val targetValue = event.playPara2.first().toInt()

        val effectStart = remember { mutableStateOf(false) }

        LaunchedEffect(ally1.value) {
            effectStart.value = ally1.value != null
        }
        BasicEventContentWithBackground(event, isShowEffect = effectStart.value, showEventIcon = false) {
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.SpaceEvenly
            ) {
                val myValue = BattleManager.adventureProps.value.getPropertyByTarget(propertyEnum)
                val gap = if (myValue > targetValue) 0 else targetValue - myValue
                val rate = max(0.0, 100.0 - gap.toFloat() * decrease)
                Spacer(modifier = Modifier.size(paddingMedium))
                Text(
                    text = stringResource(id = R.string.success_rate) + "：${rate.realValueToDotWithOneDigits()}%",
                    style = MaterialTheme.typography.h3, color = Color.Black
                )
                Text(
                    text = stringResource(id = R.string.quality_level_required) + "：${quality}",
                    style = MaterialTheme.typography.h3,
                    color = Color.Black
                )
                ally1.value?.let {
                    SingleHeroView(hero = it, itemSize = ItemSize.LargePlus) {
                        showSelect1()
                    }
                } ?: EmptyIconView(itemSize = ItemSize.LargePlus) {
                    showSelect1()
                }
                Text(
                    text = stringResource(id = R.string.ask_for) + "：${AdventureProps.rolePropertyName(propertyEnum)}${targetValue}",
                    style = MaterialTheme.typography.h3, color = Color.Black
                )
                GameButton(text = stringResource(id = R.string.made_my_choice), buttonStyle = ButtonStyle.Orange, enabled = ally1.value != null) {
                    GameApp.globalScope.launch(Dispatchers.Main) {
                        if (ally1.value == null) {
                            GameApp.instance.getWrapString(R.string.select_negotiation_heroes).toast()
                        } else {
                            if (!eventFinished.value) {
                                eventFinished.value = true
                                if (rate.chance()) {
                                    GameApp.instance.getWrapString(R.string.negotiation_success).toast()
                                    eventResult.value = true
                                } else {
                                    GameApp.instance.getWrapString(R.string.negotiation_failed).toast()
                                    eventResult.value = false
                                }
                                BattleManager.dropHeroFromGame(ally1.value!!)
                                EventManager.doEventResult(
                                    event, eventResult.value
                                )
                            }
                        }
                    }
                }
                Spacer(modifier = Modifier.size(paddingMedium))
            }
        }
    }

    override fun onEventSelect(event: Event) {
        qualityTarget.value = event.playPara1[1]
    }
}