package com.moyu.chuanqirensheng.logic.event

import com.moyu.chuanqirensheng.logic.story.StoryManager
import com.moyu.core.logic.enemy.DefaultEnemyCreator
import com.moyu.core.model.event.Event
import com.moyu.core.model.race.Race
import com.moyu.core.model.role.Role

fun createEnemyRole(race: Race, event: Event): Role {
    val isDifficult = StoryManager.isCurrentDifficultMode()
    val diffProperty = event.getDiffProperty()
    val extraHard = if (isDifficult) emptyList() else emptyList<Int>()
    val extraChallenge = emptyList<Int>()
    return DefaultEnemyCreator.create(
        race,
        diffProperty,
        extraHard + extraChallenge
    )
}