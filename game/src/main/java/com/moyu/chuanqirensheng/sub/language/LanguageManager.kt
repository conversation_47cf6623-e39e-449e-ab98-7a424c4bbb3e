package com.moyu.chuanqirensheng.sub.language

import android.content.Context
import android.content.res.Configuration
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import com.moyu.chuanqirensheng.R
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.datastore.KEY_LANGUAGE
import com.moyu.chuanqirensheng.datastore.dataStore
import com.moyu.chuanqirensheng.datastore.getStringFlowByKey
import com.moyu.chuanqirensheng.datastore.mapData
import com.moyu.chuanqirensheng.datastore.setStringValueByKey
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.chuanqirensheng.ui.theme.padding8
import com.moyu.chuanqirensheng.util.triggerRebirth
import com.moyu.core.AD_UNIT_ID_T1
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.launch
import java.util.Locale


val t1Countries = listOf("US", "JP", "KR", "DE", "FR", "GB", "AU", "CA", "TW", "HK")
val t2Countries = listOf(
    "BR", // Brazil
    "CN", // China
    "CR", // Costa Rica
    "EG", // Egypt
    "EE", // Estonia
    "GR", // Greece
    "IL", // Israel
    "MX", // Mexico
    "PL", // Poland
    "QA", // Qatar
    "TH", // Thailand
    "TR", // Turkey
    "ZA", // South Africa (Primary language for many)
    "MY", // Malaysia
    "SG", // Singapore
    "LK", // Sri Lanka
    "AE", // United Arab Emirates
    "BH", // Bahrain
    "OM", // Oman
    "MU"  // Mauritius
)
val t3Countries = listOf(
    "ID", // Indonesia
    "PH", // Philippines
    "VN", // Vietnam
    "IN", // India
    "AR", // Argentina
    "KE", // Kenya
    "NG", // Nigeria
    "GH", // Ghana
    "TZ", // Tanzania
    "UG", // Uganda
    "ZM", // Zambia
    "ZW", // Zimbabwe
    "PK", // Pakistan
    "BD", // Bangladesh
)

object LanguageManager {
    val languages = arrayOf("English", "中文")
    val languageCodes = arrayOf("en", "zh-rTW")
    val selectedLanguage = mutableStateOf("")

    suspend fun init() {
        mapData.putAll(
            GameApp.instance.dataStore.data.first().asMap()
                .map { it.key.name to it.value.toString() })
        if (GameApp.instance.resources.getBoolean(R.bool.has_google_service)) {
            if (getStringFlowByKey(KEY_LANGUAGE).isEmpty() || getStringFlowByKey(KEY_LANGUAGE) !in languageCodes) {
                val language = Locale.getDefault().language
                languageCodes.forEachIndexed { index, code ->
                    if (language.startsWith(code)) {
                        selectedLanguage.value = languageCodes[index]
                    } else if (language.startsWith("zh")) {
                        // 多种中文都用繁体
                        selectedLanguage.value = languageCodes[1]
                    }
                }
                if (selectedLanguage.value.isEmpty()) {
                    selectedLanguage.value = languageCodes[0]
                }
                setStringValueByKey(KEY_LANGUAGE, selectedLanguage.value)
            } else {
                selectedLanguage.value = getStringFlowByKey(KEY_LANGUAGE)
            }
            setLocale(GameApp.instance, selectedLanguage.value)
        } else {
            selectedLanguage.value = languageCodes[1]
            setLocale(GameApp.instance, selectedLanguage.value)
        }
    }


    @Composable
    fun LanguageSelectorView() {
        val showLanguage = remember {
            mutableStateOf(false)
        }
        if (showLanguage.value) {
            languages.forEachIndexed { index, language ->
                val selected = languageCodes[index] == selectedLanguage.value
                GameButton(
                    text = language,
                    buttonStyle = if (selected) ButtonStyle.Orange else ButtonStyle.Blue
                ) {
                    if (selectedLanguage.value != languageCodes[index]) {
                        selectedLanguage.value = languageCodes[index]
                        setStringValueByKey(KEY_LANGUAGE, selectedLanguage.value)
                        updateLocale(GameApp.instance, languageCodes[index])
                    }
                    showLanguage.value = false
                }
            }
        } else {
            EffectButton(
                Modifier.padding(start = padding19, top = padding8, end = padding8),
                onClick = {
                    showLanguage.value = !showLanguage.value
                }) {
                Text(
                    text = stringResource(R.string.language_title),
                    style = MaterialTheme.typography.h1
                )
            }
        }
    }

    fun setLocale(context: Context, languageCode: String) {
        val locale = Locale(languageCode)
        Locale.setDefault(locale)
        val config = Configuration(context.resources.configuration)
        config.setLocale(locale)
        context.resources.updateConfiguration(config, context.resources.displayMetrics)
    }

    fun updateLocale(context: Context, languageCode: String) {
        setLocale(context, languageCode)
        // 重新启动活动以应用新的语言设置
        GameApp.globalScope.launch {
            delay(500)
            triggerRebirth()
        }
    }

    fun getLocalizedAssetFileName(fileName: String): String {
        val language = selectedLanguage.value
        languageCodes.forEach {
            if (language == it) {
                if (language == "zh-rTW") {
                    // 默认
                    return fileName
                }
                return it + "_" + fileName
            }
        }
        return fileName
    }

    fun getCountryAdId(): String {
        return AD_UNIT_ID_T1
//        val level = getCountryLevel()
//        return when (level) {
//            1 -> AD_UNIT_ID_T1
//            2 -> AD_UNIT_ID_T2
//            3 -> AD_UNIT_ID_T3
//            else -> AD_UNIT_ID_T1
//        }
    }

    fun getCountryLevel(): Int {
        val locale = Locale.getDefault()
        val country = locale.country.uppercase()
        return when (country) {
            in t1Countries -> {
                1
            }
            in t2Countries -> {
                2
            }
            in t3Countries -> {
                3
            }
            else -> {
                1
            }
        }
    }

    fun isSelectedChinese() = selectedLanguage.value == languageCodes[1]


    fun getRegionName(): String {
        // 获取当前系统的区域名称
        return Locale.getDefault().getDisplayCountry(Locale.getDefault()) // 根据默认语言环境获取区域名称
    }

    fun getRegionNameInEnglish(): String {
        // 获取区域名称并以英语显示
        return Locale.getDefault().getDisplayCountry(Locale.ENGLISH) // 强制以英语显示区域名称
    }
}