# -*- coding: utf-8 -*-
"""
Spyder Editor

This is a temporary script file.
"""
import os 
from os import path
from PIL import Image

imagePath = r"C:\Users\<USER>\Desktop\test"
targetPath = r"C:\Users\<USER>\Desktop\test-out"

for root, dirs, files in os.walk(imagePath):
    for f in files:
        m = os.path.join(root, f)
        print("m=",m)
        ss=os.path.splitext(m)
        print("ss = ",ss)
        dirname = ss[0]#文件夹
        print("dirname = ",dirname)
        a = os.path.basename(m)#文件名，带后缀
        file_name = a.split('.')[0]
        houzui = a.split('.')[0]
        print("file_name = ",file_name , "--------houzui = ",houzui)
        print("a=",a)
        
        
        try:
            im = Image.open(m)
            im.load()
            targetFilePath = path.join(targetPath, file_name + ".png")
            print(targetFilePath)
            im.save(targetFilePath)
        except Exception as e :
           print(e)