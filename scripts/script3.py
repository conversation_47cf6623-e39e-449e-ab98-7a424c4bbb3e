# -*- coding: utf-8 -*-
"""
Created on Sun Jul 31 17:12:59 2022

@author: tujiu
"""




import os 
from os import path
from PIL import Image
import tinify

tinify.key = "w7pVNd8hhNtcYSpPl34TQgS1MQk9Pywp" # AppKey

imagePath = r"D:\projects\wujindeyuansushi\gamecoredemo\src\main\res\drawable-xhdpi"

for parent, _, fileNames in os.walk(imagePath):
   for fileName in fileNames:
       print(fileName)
       realFilePath = path.join(imagePath, fileName)
       source = tinify.from_file(realFilePath)
       source.to_file(realFilePath)